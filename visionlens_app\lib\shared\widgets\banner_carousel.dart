import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';

class BannerCarousel extends StatefulWidget {
  final List<BannerItem>? banners;
  final double height;
  final bool autoPlay;
  final Duration autoPlayInterval;

  const BannerCarousel({
    super.key,
    this.banners,
    this.height = 200,
    this.autoPlay = true,
    this.autoPlayInterval = const Duration(seconds: 4),
  });

  @override
  State<BannerCarousel> createState() => _BannerCarouselState();
}

class _BannerCarouselState extends State<BannerCarousel> {
  int _currentIndex = 0;
  late List<BannerItem> _banners;
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _banners = widget.banners ?? _getDummyBanners();
    _pageController = PageController();

    if (widget.autoPlay && _banners.length > 1) {
      _startAutoPlay();
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _startAutoPlay() {
    Future.delayed(widget.autoPlayInterval, () {
      if (mounted && _pageController.hasClients) {
        final nextIndex = (_currentIndex + 1) % _banners.length;
        _pageController.animateToPage(
          nextIndex,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
        _startAutoPlay();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_banners.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          // Page View
          SizedBox(
            height: widget.height,
            child: PageView.builder(
              controller: _pageController,
              itemCount: _banners.length,
              onPageChanged: (index) {
                setState(() {
                  _currentIndex = index;
                });
              },
              itemBuilder: (context, index) {
                final banner = _banners[index];
                return _buildBannerItem(banner);
              },
            ),
          ),

          // Indicators
          if (_banners.length > 1) ...[
            const SizedBox(height: 12),
            _buildIndicators(),
          ],
        ],
      ),
    );
  }

  Widget _buildBannerItem(BannerItem banner) {
    return GestureDetector(
      onTap: banner.onTap,
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: AppColors.shadow,
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16),
          child: Stack(
            fit: StackFit.expand,
            children: [
              // Background Image
              banner.imageUrl != null
                  ? CachedNetworkImage(
                      imageUrl: banner.imageUrl!,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        color: AppColors.surfaceVariant,
                        child: const Center(child: CircularProgressIndicator()),
                      ),
                      errorWidget: (context, url, error) => Container(
                        color: AppColors.surfaceVariant,
                        child: const Icon(
                          Icons.image_not_supported,
                          color: AppColors.textSecondary,
                          size: 40,
                        ),
                      ),
                    )
                  : Container(
                      decoration: BoxDecoration(
                        gradient: banner.gradient ?? AppColors.primaryGradient,
                      ),
                    ),

              // Overlay
              if (banner.showOverlay)
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withOpacity(0.6),
                      ],
                    ),
                  ),
                ),

              // Content
              if (banner.title != null || banner.subtitle != null)
                Positioned(
                  bottom: 20,
                  left: 20,
                  right: 20,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (banner.title != null)
                        Text(
                          banner.title!,
                          style: AppTextStyles.headlineSmall.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      if (banner.subtitle != null) ...[
                        const SizedBox(height: 8),
                        Text(
                          banner.subtitle!,
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: Colors.white.withOpacity(0.9),
                          ),
                        ),
                      ],
                      if (banner.buttonText != null) ...[
                        const SizedBox(height: 12),
                        ElevatedButton(
                          onPressed: banner.onButtonPressed ?? banner.onTap,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.white,
                            foregroundColor: AppColors.primary,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: Text(banner.buttonText!),
                        ),
                      ],
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildIndicators() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: _banners.asMap().entries.map((entry) {
        final index = entry.key;
        final isActive = index == _currentIndex;

        return Container(
          width: isActive ? 24 : 8,
          height: 8,
          margin: const EdgeInsets.symmetric(horizontal: 4),
          decoration: BoxDecoration(
            color: isActive ? AppColors.primary : AppColors.border,
            borderRadius: BorderRadius.circular(4),
          ),
        );
      }).toList(),
    );
  }

  List<BannerItem> _getDummyBanners() {
    return [
      BannerItem(
        title: 'خصم 30% على جميع العدسات اليومية',
        subtitle: 'عرض محدود لفترة قصيرة',
        buttonText: 'تسوق الآن',
        gradient: const LinearGradient(
          colors: [AppColors.primary, AppColors.primaryDark],
        ),
        onTap: () {
          // TODO: Navigate to daily lenses
        },
      ),
      BannerItem(
        title: 'عدسات ملونة جديدة',
        subtitle: 'اكتشف مجموعتنا الجديدة من العدسات الملونة',
        buttonText: 'اكتشف المزيد',
        gradient: const LinearGradient(
          colors: [AppColors.secondary, AppColors.secondaryDark],
        ),
        onTap: () {
          // TODO: Navigate to colored lenses
        },
      ),
      BannerItem(
        title: 'توصيل مجاني',
        subtitle: 'للطلبات أكثر من 50,000 د.ع',
        buttonText: 'ابدأ التسوق',
        gradient: const LinearGradient(
          colors: [AppColors.success, AppColors.successDark],
        ),
        onTap: () {
          // TODO: Navigate to products
        },
      ),
    ];
  }
}

class BannerItem {
  final String? title;
  final String? subtitle;
  final String? buttonText;
  final String? imageUrl;
  final LinearGradient? gradient;
  final bool showOverlay;
  final VoidCallback? onTap;
  final VoidCallback? onButtonPressed;

  const BannerItem({
    this.title,
    this.subtitle,
    this.buttonText,
    this.imageUrl,
    this.gradient,
    this.showOverlay = true,
    this.onTap,
    this.onButtonPressed,
  });
}
