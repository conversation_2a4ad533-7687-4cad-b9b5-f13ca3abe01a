import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../core/providers/admin_auth_provider.dart';
import 'admin_login_screen.dart';
import 'widgets/admin_sidebar.dart';
import 'widgets/dashboard_stats.dart';
import 'widgets/recent_orders.dart';
import 'widgets/quick_actions.dart';

class AdminDashboardScreen extends ConsumerStatefulWidget {
  const AdminDashboardScreen({super.key});

  @override
  ConsumerState<AdminDashboardScreen> createState() => _AdminDashboardScreenState();
}

class _AdminDashboardScreenState extends ConsumerState<AdminDashboardScreen> {
  int _selectedIndex = 0;
  bool _isSidebarExpanded = true;

  final List<String> _pageNames = [
    'لوحة التحكم',
    'إدارة المنتجات',
    'إدارة الطلبات',
    'إدارة العملاء',
    'التقارير',
    'الإعدادات',
  ];

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(adminAuthProvider);
    final admin = authState.admin;

    // Check if admin is logged in
    if (!authState.isLoggedIn || admin == null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const AdminLoginScreen()),
        );
      });
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      backgroundColor: AppColors.background,
      body: Row(
        children: [
          // Sidebar
          AdminSidebar(
            selectedIndex: _selectedIndex,
            isExpanded: _isSidebarExpanded,
            onItemSelected: (index) {
              setState(() {
                _selectedIndex = index;
              });
            },
            onToggleExpanded: () {
              setState(() {
                _isSidebarExpanded = !_isSidebarExpanded;
              });
            },
          ),
          
          // Main Content
          Expanded(
            child: Column(
              children: [
                // Top App Bar
                _buildTopAppBar(admin),
                
                // Page Content
                Expanded(
                  child: _buildPageContent(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopAppBar(admin) {
    return Container(
      height: 70,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      decoration: BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Page Title
          Text(
            _pageNames[_selectedIndex],
            style: AppTextStyles.headlineSmall.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const Spacer(),
          
          // Notifications
          IconButton(
            onPressed: () {
              // TODO: Show notifications
            },
            icon: Stack(
              children: [
                const Icon(Icons.notifications_outlined),
                Positioned(
                  right: 0,
                  top: 0,
                  child: Container(
                    width: 8,
                    height: 8,
                    decoration: const BoxDecoration(
                      color: AppColors.error,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Admin Profile
          PopupMenuButton<String>(
            offset: const Offset(0, 50),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircleAvatar(
                  radius: 18,
                  backgroundColor: AppColors.primary.withValues(alpha: 0.1),
                  child: Text(
                    admin.name[0],
                    style: AppTextStyles.labelMedium.copyWith(
                      color: AppColors.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      admin.name,
                      style: AppTextStyles.labelMedium.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      admin.role == 'super_admin' ? 'مدير عام' : 
                      admin.role == 'admin' ? 'مدير' :
                      admin.role == 'manager' ? 'مدير' : 'دعم فني',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
                const SizedBox(width: 4),
                const Icon(Icons.keyboard_arrow_down),
              ],
            ),
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'profile',
                child: ListTile(
                  leading: Icon(Icons.person_outline),
                  title: Text('الملف الشخصي'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'settings',
                child: ListTile(
                  leading: Icon(Icons.settings_outlined),
                  title: Text('الإعدادات'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuDivider(),
              const PopupMenuItem(
                value: 'logout',
                child: ListTile(
                  leading: Icon(Icons.logout, color: AppColors.error),
                  title: Text('تسجيل الخروج', style: TextStyle(color: AppColors.error)),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
            onSelected: (value) {
              switch (value) {
                case 'profile':
                  // TODO: Show profile dialog
                  break;
                case 'settings':
                  setState(() {
                    _selectedIndex = 5; // Settings page
                  });
                  break;
                case 'logout':
                  _handleLogout();
                  break;
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPageContent() {
    switch (_selectedIndex) {
      case 0:
        return _buildDashboardContent();
      case 1:
        return _buildProductsContent();
      case 2:
        return _buildOrdersContent();
      case 3:
        return _buildCustomersContent();
      case 4:
        return _buildReportsContent();
      case 5:
        return _buildSettingsContent();
      default:
        return _buildDashboardContent();
    }
  }

  Widget _buildDashboardContent() {
    return const SingleChildScrollView(
      padding: EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Statistics Cards
          DashboardStats(),
          
          SizedBox(height: 24),
          
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Recent Orders
              Expanded(
                flex: 2,
                child: RecentOrders(),
              ),
              
              SizedBox(width: 24),
              
              // Quick Actions
              Expanded(
                flex: 1,
                child: QuickActions(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProductsContent() {
    return const Center(
      child: Text('إدارة المنتجات - قيد التطوير'),
    );
  }

  Widget _buildOrdersContent() {
    return const Center(
      child: Text('إدارة الطلبات - قيد التطوير'),
    );
  }

  Widget _buildCustomersContent() {
    return const Center(
      child: Text('إدارة العملاء - قيد التطوير'),
    );
  }

  Widget _buildReportsContent() {
    return const Center(
      child: Text('التقارير - قيد التطوير'),
    );
  }

  Widget _buildSettingsContent() {
    return const Center(
      child: Text('الإعدادات - قيد التطوير'),
    );
  }

  void _handleLogout() async {
    final success = await ref.read(adminAuthProvider.notifier).logout();
    if (success && mounted) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => const AdminLoginScreen()),
      );
    }
  }
}
