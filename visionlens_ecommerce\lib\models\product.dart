class Product {
  final String id;
  final String name;
  final String description;
  final double price;
  final String brand;
  final String category;
  final List<String> images;
  final double rating;
  final int reviewCount;
  final bool isAvailable;
  final int stockQuantity;
  final List<String> features;
  final Map<String, dynamic> specifications;

  Product({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.brand,
    required this.category,
    required this.images,
    this.rating = 0.0,
    this.reviewCount = 0,
    this.isAvailable = true,
    this.stockQuantity = 0,
    this.features = const [],
    this.specifications = const {},
  });

  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      price: (json['price'] ?? 0).toDouble(),
      brand: json['brand'] ?? '',
      category: json['category'] ?? '',
      images: List<String>.from(json['images'] ?? []),
      rating: (json['rating'] ?? 0).toDouble(),
      reviewCount: json['reviewCount'] ?? 0,
      isAvailable: json['isAvailable'] ?? true,
      stockQuantity: json['stockQuantity'] ?? 0,
      features: List<String>.from(json['features'] ?? []),
      specifications: json['specifications'] ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'price': price,
      'brand': brand,
      'category': category,
      'images': images,
      'rating': rating,
      'reviewCount': reviewCount,
      'isAvailable': isAvailable,
      'stockQuantity': stockQuantity,
      'features': features,
      'specifications': specifications,
    };
  }

  Product copyWith({
    String? id,
    String? name,
    String? description,
    double? price,
    String? brand,
    String? category,
    List<String>? images,
    double? rating,
    int? reviewCount,
    bool? isAvailable,
    int? stockQuantity,
    List<String>? features,
    Map<String, dynamic>? specifications,
  }) {
    return Product(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      brand: brand ?? this.brand,
      category: category ?? this.category,
      images: images ?? this.images,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      isAvailable: isAvailable ?? this.isAvailable,
      stockQuantity: stockQuantity ?? this.stockQuantity,
      features: features ?? this.features,
      specifications: specifications ?? this.specifications,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Product && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Product(id: $id, name: $name, price: $price)';
  }
}

class Category {
  final String id;
  final String name;
  final String description;
  final String image;
  final int productCount;

  Category({
    required this.id,
    required this.name,
    required this.description,
    required this.image,
    this.productCount = 0,
  });

  factory Category.fromJson(Map<String, dynamic> json) {
    return Category(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      image: json['image'] ?? '',
      productCount: json['productCount'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'image': image,
      'productCount': productCount,
    };
  }
}
