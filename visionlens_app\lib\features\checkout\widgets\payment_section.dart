import 'package:flutter/material.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';

class PaymentSection extends StatefulWidget {
  final String selectedMethod;
  final Function(String) onMethodSelected;
  final Function(Map<String, dynamic>?) onPaymentDetails;

  const PaymentSection({
    super.key,
    required this.selectedMethod,
    required this.onMethodSelected,
    required this.onPaymentDetails,
  });

  @override
  State<PaymentSection> createState() => _PaymentSectionState();
}

class _PaymentSectionState extends State<PaymentSection> {
  final _cardNumberController = TextEditingController();
  final _expiryController = TextEditingController();
  final _cvvController = TextEditingController();
  final _cardHolderController = TextEditingController();

  @override
  void dispose() {
    _cardNumberController.dispose();
    _expiryController.dispose();
    _cvvController.dispose();
    _cardHolderController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'طريقة الدفع',
          style: AppTextStyles.headlineSmall.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        
        const SizedBox(height: 20),
        
        // Payment Methods
        ...AppConstants.paymentMethods.map((method) => _buildPaymentMethodCard(method)),
        
        const SizedBox(height: 20),
        
        // Payment Details Form
        if (widget.selectedMethod == 'credit_card') _buildCreditCardForm(),
        if (widget.selectedMethod == 'bank_transfer') _buildBankTransferInfo(),
        if (widget.selectedMethod == 'digital_wallet') _buildDigitalWalletOptions(),
      ],
    );
  }

  Widget _buildPaymentMethodCard(String method) {
    final isSelected = widget.selectedMethod == method;
    final methodName = AppConstants.paymentMethodsArabic[method] ?? method;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () {
          widget.onMethodSelected(method);
          widget.onPaymentDetails(null); // Clear previous details
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected ? AppColors.primary : AppColors.border,
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Row(
            children: [
              Icon(
                isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                color: isSelected ? AppColors.primary : AppColors.textSecondary,
              ),
              
              const SizedBox(width: 12),
              
              Icon(
                _getPaymentMethodIcon(method),
                color: isSelected ? AppColors.primary : AppColors.textSecondary,
              ),
              
              const SizedBox(width: 12),
              
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      methodName,
                      style: AppTextStyles.titleMedium.copyWith(
                        fontWeight: FontWeight.w600,
                        color: isSelected ? AppColors.primary : AppColors.textPrimary,
                      ),
                    ),
                    Text(
                      _getPaymentMethodDescription(method),
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              
              if (method == 'cash_on_delivery')
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.success.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'الأكثر شيوعاً',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.success,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCreditCardForm() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'بيانات البطاقة الائتمانية',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          
          const SizedBox(height: 20),
          
          // Card Number
          TextFormField(
            controller: _cardNumberController,
            decoration: const InputDecoration(
              labelText: 'رقم البطاقة',
              hintText: '1234 5678 9012 3456',
              prefixIcon: Icon(Icons.credit_card),
            ),
            keyboardType: TextInputType.number,
            onChanged: (value) => _updatePaymentDetails(),
          ),
          
          const SizedBox(height: 16),
          
          // Card Holder Name
          TextFormField(
            controller: _cardHolderController,
            decoration: const InputDecoration(
              labelText: 'اسم حامل البطاقة',
              prefixIcon: Icon(Icons.person),
            ),
            onChanged: (value) => _updatePaymentDetails(),
          ),
          
          const SizedBox(height: 16),
          
          // Expiry and CVV
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _expiryController,
                  decoration: const InputDecoration(
                    labelText: 'تاريخ الانتهاء',
                    hintText: 'MM/YY',
                    prefixIcon: Icon(Icons.calendar_today),
                  ),
                  keyboardType: TextInputType.number,
                  onChanged: (value) => _updatePaymentDetails(),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  controller: _cvvController,
                  decoration: const InputDecoration(
                    labelText: 'CVV',
                    hintText: '123',
                    prefixIcon: Icon(Icons.security),
                  ),
                  keyboardType: TextInputType.number,
                  obscureText: true,
                  onChanged: (value) => _updatePaymentDetails(),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          // Security Notice
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.info.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.security,
                  color: AppColors.info,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'بياناتك محمية بتشفير SSL آمن',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.info,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBankTransferInfo() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'معلومات التحويل البنكي',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          
          const SizedBox(height: 16),
          
          _buildBankInfo('اسم البنك', 'بنك بغداد'),
          _buildBankInfo('رقم الحساب', '****************'),
          _buildBankInfo('اسم المستفيد', 'شركة عدستي للعدسات اللاصقة'),
          _buildBankInfo('IBAN', 'IQ98BBAG****************'),
          
          const SizedBox(height: 16),
          
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.warning.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.info,
                      color: AppColors.warning,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'تعليمات مهمة:',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.warning,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  '• يرجى إرسال إيصال التحويل عبر الواتساب\n'
                  '• سيتم تأكيد الطلب خلال 24 ساعة\n'
                  '• احتفظ برقم المرجع للمتابعة',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.warning,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBankInfo(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ),
          IconButton(
            onPressed: () {
              // TODO: Copy to clipboard
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم نسخ المعلومات'),
                  backgroundColor: AppColors.success,
                ),
              );
            },
            icon: const Icon(Icons.copy, size: 16),
            style: IconButton.styleFrom(
              foregroundColor: AppColors.textSecondary,
              minimumSize: const Size(32, 32),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDigitalWalletOptions() {
    final wallets = [
      {'name': 'ZainCash', 'icon': Icons.account_balance_wallet},
      {'name': 'AsiaHawala', 'icon': Icons.account_balance},
      {'name': 'FastPay', 'icon': Icons.payment},
    ];

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'اختر المحفظة الرقمية',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          
          const SizedBox(height: 16),
          
          ...wallets.map((wallet) => Container(
            margin: const EdgeInsets.only(bottom: 8),
            child: ListTile(
              leading: Icon(wallet['icon'] as IconData),
              title: Text(wallet['name'] as String),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () {
                // TODO: Implement wallet selection
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('${wallet['name']} قريباً...'),
                    backgroundColor: AppColors.info,
                  ),
                );
              },
            ),
          )),
        ],
      ),
    );
  }

  IconData _getPaymentMethodIcon(String method) {
    switch (method) {
      case 'cash_on_delivery':
        return Icons.money;
      case 'credit_card':
        return Icons.credit_card;
      case 'bank_transfer':
        return Icons.account_balance;
      case 'digital_wallet':
        return Icons.account_balance_wallet;
      default:
        return Icons.payment;
    }
  }

  String _getPaymentMethodDescription(String method) {
    switch (method) {
      case 'cash_on_delivery':
        return 'ادفع نقداً عند استلام الطلب';
      case 'credit_card':
        return 'فيزا، ماستركارد، أو بطاقات محلية';
      case 'bank_transfer':
        return 'تحويل مباشر إلى حساب الشركة';
      case 'digital_wallet':
        return 'ZainCash، AsiaHawala، وغيرها';
      default:
        return '';
    }
  }

  void _updatePaymentDetails() {
    if (widget.selectedMethod == 'credit_card') {
      widget.onPaymentDetails({
        'cardNumber': _cardNumberController.text,
        'cardHolder': _cardHolderController.text,
        'expiry': _expiryController.text,
        'cvv': _cvvController.text,
      });
    }
  }
}
