import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/constants/app_constants.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../shared/widgets/custom_app_bar.dart';

class ProfileScreen extends ConsumerStatefulWidget {
  const ProfileScreen({super.key});

  @override
  ConsumerState<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends ConsumerState<ProfileScreen> {
  bool _isLoggedIn = false; // TODO: Get from auth provider

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: CustomAppBar(
        title: 'الملف الشخصي',
        showBackButton: false,
        showCartIcon: true,
      ),
      body: _isLoggedIn ? _buildLoggedInProfile() : _buildGuestProfile(),
    );
  }

  Widget _buildLoggedInProfile() {
    return SingleChildScrollView(
      child: Column(
        children: [
          // Profile Header
          _buildProfileHeader(),
          
          const SizedBox(height: 20),
          
          // Profile Menu Items
          _buildMenuSection('الحساب', [
            _buildMenuItem(
              icon: Icons.person_outline,
              title: 'معلومات الحساب',
              subtitle: 'تعديل البيانات الشخصية',
              onTap: () => _navigateToAccountInfo(),
            ),
            _buildMenuItem(
              icon: Icons.location_on_outlined,
              title: 'العناوين',
              subtitle: 'إدارة عناوين التوصيل',
              onTap: () => _navigateToAddresses(),
            ),
            _buildMenuItem(
              icon: Icons.security,
              title: 'الأمان',
              subtitle: 'تغيير كلمة المرور والأمان',
              onTap: () => _navigateToSecurity(),
            ),
          ]),
          
          const SizedBox(height: 20),
          
          // Orders Section
          _buildMenuSection('الطلبات', [
            _buildMenuItem(
              icon: Icons.shopping_bag_outlined,
              title: 'طلباتي',
              subtitle: 'عرض وتتبع الطلبات',
              onTap: () => _navigateToOrders(),
            ),
            _buildMenuItem(
              icon: Icons.history,
              title: 'سجل الطلبات',
              subtitle: 'جميع الطلبات السابقة',
              onTap: () => _navigateToOrderHistory(),
            ),
            _buildMenuItem(
              icon: Icons.assignment_return,
              title: 'الإرجاع والاستبدال',
              subtitle: 'طلبات الإرجاع والاستبدال',
              onTap: () => _navigateToReturns(),
            ),
          ]),
          
          const SizedBox(height: 20),
          
          // Preferences Section
          _buildMenuSection('التفضيلات', [
            _buildMenuItem(
              icon: Icons.notifications_outlined,
              title: 'الإشعارات',
              subtitle: 'إعدادات الإشعارات',
              onTap: () => _navigateToNotifications(),
            ),
            _buildMenuItem(
              icon: Icons.language,
              title: 'اللغة',
              subtitle: 'العربية',
              onTap: () => _navigateToLanguage(),
            ),
            _buildMenuItem(
              icon: Icons.dark_mode_outlined,
              title: 'المظهر',
              subtitle: 'فاتح / داكن',
              onTap: () => _navigateToTheme(),
            ),
          ]),
          
          const SizedBox(height: 20),
          
          // Support Section
          _buildMenuSection('الدعم', [
            _buildMenuItem(
              icon: Icons.help_outline,
              title: 'مركز المساعدة',
              subtitle: 'الأسئلة الشائعة والدعم',
              onTap: () => _navigateToHelp(),
            ),
            _buildMenuItem(
              icon: Icons.chat_outlined,
              title: 'تواصل معنا',
              subtitle: 'دردشة مباشرة أو اتصال',
              onTap: () => _navigateToContact(),
            ),
            _buildMenuItem(
              icon: Icons.star_outline,
              title: 'قيم التطبيق',
              subtitle: 'شاركنا رأيك',
              onTap: () => _rateApp(),
            ),
          ]),
          
          const SizedBox(height: 20),
          
          // About Section
          _buildMenuSection('حول التطبيق', [
            _buildMenuItem(
              icon: Icons.info_outline,
              title: 'معلومات التطبيق',
              subtitle: 'الإصدار ${AppConstants.appVersion}',
              onTap: () => _navigateToAbout(),
            ),
            _buildMenuItem(
              icon: Icons.privacy_tip_outlined,
              title: 'سياسة الخصوصية',
              subtitle: 'شروط الاستخدام والخصوصية',
              onTap: () => _navigateToPrivacy(),
            ),
          ]),
          
          const SizedBox(height: 30),
          
          // Logout Button
          _buildLogoutButton(),
          
          const SizedBox(height: 30),
        ],
      ),
    );
  }

  Widget _buildGuestProfile() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Guest Icon
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.person_outline,
                size: 60,
                color: AppColors.primary,
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Title
            Text(
              'مرحباً بك في عدستي',
              style: AppTextStyles.headlineSmall.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 12),
            
            // Description
            Text(
              'سجل دخولك للاستفادة من جميع المميزات\nوتتبع طلباتك وحفظ المفضلة',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 32),
            
            // Login Button
            SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton(
                onPressed: _navigateToLogin,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  'تسجيل الدخول',
                  style: AppTextStyles.buttonPrimary,
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Register Button
            SizedBox(
              width: double.infinity,
              height: 50,
              child: OutlinedButton(
                onPressed: _navigateToRegister,
                style: OutlinedButton.styleFrom(
                  foregroundColor: AppColors.primary,
                  side: const BorderSide(color: AppColors.primary),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  'إنشاء حساب جديد',
                  style: AppTextStyles.buttonSecondary,
                ),
              ),
            ),
            
            const SizedBox(height: 32),
            
            // Guest Options
            Text(
              'أو تصفح كضيف',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            
            const SizedBox(height: 16),
            
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildGuestOption(
                  icon: Icons.help_outline,
                  label: 'المساعدة',
                  onTap: _navigateToHelp,
                ),
                _buildGuestOption(
                  icon: Icons.chat_outlined,
                  label: 'تواصل معنا',
                  onTap: _navigateToContact,
                ),
                _buildGuestOption(
                  icon: Icons.info_outline,
                  label: 'حول التطبيق',
                  onTap: _navigateToAbout,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: AppColors.primaryGradient,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          // Profile Image
          CircleAvatar(
            radius: 40,
            backgroundColor: Colors.white.withOpacity(0.2),
            child: const Icon(
              Icons.person,
              size: 40,
              color: Colors.white,
            ),
          ),
          
          const SizedBox(width: 16),
          
          // User Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'أحمد محمد', // TODO: Get from user data
                  style: AppTextStyles.titleLarge.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '<EMAIL>', // TODO: Get from user data
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'عضو ذهبي', // TODO: Get from user data
                    style: AppTextStyles.bodySmall.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // Edit Button
          IconButton(
            onPressed: _navigateToAccountInfo,
            icon: const Icon(
              Icons.edit,
              color: Colors.white,
            ),
            style: IconButton.styleFrom(
              backgroundColor: Colors.white.withOpacity(0.2),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMenuSection(String title, List<Widget> items) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              title,
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          ...items,
        ],
      ),
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    Widget? trailing,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: AppColors.primary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: AppColors.primary,
          size: 20,
        ),
      ),
      title: Text(
        title,
        style: AppTextStyles.titleSmall.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: AppTextStyles.bodySmall.copyWith(
          color: AppColors.textSecondary,
        ),
      ),
      trailing: trailing ?? const Icon(
        Icons.arrow_forward_ios,
        size: 16,
        color: AppColors.textSecondary,
      ),
      onTap: onTap,
    );
  }

  Widget _buildGuestOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: AppColors.primary,
              size: 24,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLogoutButton() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: SizedBox(
        width: double.infinity,
        height: 50,
        child: OutlinedButton(
          onPressed: _logout,
          style: OutlinedButton.styleFrom(
            foregroundColor: AppColors.error,
            side: const BorderSide(color: AppColors.error),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.logout),
              const SizedBox(width: 8),
              Text(
                'تسجيل الخروج',
                style: AppTextStyles.buttonSecondary.copyWith(
                  color: AppColors.error,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Navigation Methods
  void _navigateToLogin() {
    // TODO: Navigate to login screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('الانتقال إلى صفحة تسجيل الدخول...'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _navigateToRegister() {
    // TODO: Navigate to register screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('الانتقال إلى صفحة إنشاء الحساب...'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _navigateToAccountInfo() {
    // TODO: Navigate to account info screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('الانتقال إلى معلومات الحساب...'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _navigateToAddresses() {
    // TODO: Navigate to addresses screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('الانتقال إلى العناوين...'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _navigateToSecurity() {
    // TODO: Navigate to security screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('الانتقال إلى إعدادات الأمان...'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _navigateToOrders() {
    // TODO: Navigate to orders screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('الانتقال إلى الطلبات...'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _navigateToOrderHistory() {
    // TODO: Navigate to order history screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('الانتقال إلى سجل الطلبات...'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _navigateToReturns() {
    // TODO: Navigate to returns screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('الانتقال إلى الإرجاع والاستبدال...'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _navigateToNotifications() {
    // TODO: Navigate to notifications screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('الانتقال إلى إعدادات الإشعارات...'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _navigateToLanguage() {
    // TODO: Navigate to language screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('الانتقال إلى إعدادات اللغة...'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _navigateToTheme() {
    // TODO: Navigate to theme screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('الانتقال إلى إعدادات المظهر...'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _navigateToHelp() {
    // TODO: Navigate to help screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('الانتقال إلى مركز المساعدة...'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _navigateToContact() {
    // TODO: Navigate to contact screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('الانتقال إلى صفحة التواصل...'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _navigateToAbout() {
    // TODO: Navigate to about screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('الانتقال إلى معلومات التطبيق...'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _navigateToPrivacy() {
    // TODO: Navigate to privacy screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('الانتقال إلى سياسة الخصوصية...'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _rateApp() {
    // TODO: Open app store for rating
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('شكراً لك! سيتم فتح متجر التطبيقات...'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _logout() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تسجيل الخروج'),
          content: const Text('هل أنت متأكد من رغبتك في تسجيل الخروج؟'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // TODO: Logout logic
                setState(() {
                  _isLoggedIn = false;
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم تسجيل الخروج بنجاح'),
                    backgroundColor: AppColors.success,
                  ),
                );
              },
              style: TextButton.styleFrom(
                foregroundColor: AppColors.error,
              ),
              child: const Text('تسجيل الخروج'),
            ),
          ],
        );
      },
    );
  }
}
