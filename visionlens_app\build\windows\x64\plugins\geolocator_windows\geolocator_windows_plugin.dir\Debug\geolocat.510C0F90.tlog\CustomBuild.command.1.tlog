^G:\VISIONLENSAPP\VISIONLENS_APP\WINDOWS\FLUTTER\EPHEMERAL\.PLUGIN_SYMLINKS\GEOLOCATOR_WINDOWS\WINDOWS\CMAKELISTS.TXT
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SG:/visionlensapp/visionlens_app/windows -BG:/visionlensapp/visionlens_app/build/windows/x64 --check-stamp-file G:/visionlensapp/visionlens_app/build/windows/x64/plugins/geolocator_windows/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
