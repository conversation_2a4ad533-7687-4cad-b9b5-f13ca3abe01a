import 'package:flutter/material.dart';

void main() {
  runApp(const VisionLensApp());
}

class VisionLensApp extends StatelessWidget {
  const VisionLensApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'VisionLens',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(primarySwatch: Colors.blue, fontFamily: 'Cairo'),
      home: const HomePage(),
    );
  }
}

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('VisionLens'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.visibility, size: 100, color: Colors.blue),
            SizedBox(height: 20),
            Text(
              'مرحباً بك في VisionLens',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 10),
            Text(
              'تطبيق العدسات اللاصقة الرائد',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
            SizedBox(height: 30),
            AdminButton(),
          ],
        ),
      ),
    );
  }
}

class AdminButton extends StatelessWidget {
  const AdminButton({super.key});

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: () {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const AdminLoginPage()),
        );
      },
      icon: const Icon(Icons.admin_panel_settings),
      label: const Text('لوحة تحكم المدير'),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 15),
      ),
    );
  }
}

class AdminLoginPage extends StatefulWidget {
  const AdminLoginPage({super.key});

  @override
  State<AdminLoginPage> createState() => _AdminLoginPageState();
}

class _AdminLoginPageState extends State<AdminLoginPage> {
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  void _login() {
    final email = _emailController.text;
    final password = _passwordController.text;

    // Check demo credentials
    if (email == '<EMAIL>' && password == 'admin123') {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(builder: (context) => const AdminDashboard()),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('بيانات الدخول غير صحيحة'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تسجيل دخول المدير'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.admin_panel_settings,
              size: 80,
              color: Colors.blue,
            ),
            const SizedBox(height: 30),
            TextField(
              controller: _emailController,
              decoration: const InputDecoration(
                labelText: 'البريد الإلكتروني',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.email),
              ),
            ),
            const SizedBox(height: 20),
            TextField(
              controller: _passwordController,
              obscureText: true,
              decoration: const InputDecoration(
                labelText: 'كلمة المرور',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.lock),
              ),
            ),
            const SizedBox(height: 30),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _login,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 15),
                ),
                child: const Text('تسجيل الدخول'),
              ),
            ),
            const SizedBox(height: 20),
            const Card(
              child: Padding(
                padding: EdgeInsets.all(15),
                child: Column(
                  children: [
                    Text(
                      'بيانات تجريبية:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 10),
                    Text('البريد: <EMAIL>'),
                    Text('كلمة المرور: admin123'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class AdminDashboard extends StatefulWidget {
  const AdminDashboard({super.key});

  @override
  State<AdminDashboard> createState() => _AdminDashboardState();
}

class _AdminDashboardState extends State<AdminDashboard> {
  int _selectedIndex = 0;

  final List<Widget> _pages = [
    const DashboardOverview(),
    const ProductsManagement(),
    const OrdersManagement(),
    const CustomersManagement(),
    const ReportsPage(),
    const SettingsPage(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('لوحة تحكم المدير'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () {
              Navigator.pushAndRemoveUntil(
                context,
                MaterialPageRoute(builder: (context) => const HomePage()),
                (route) => false,
              );
            },
          ),
        ],
      ),
      body: Row(
        children: [
          NavigationRail(
            selectedIndex: _selectedIndex,
            onDestinationSelected: (index) {
              setState(() {
                _selectedIndex = index;
              });
            },
            labelType: NavigationRailLabelType.none,
            destinations: const [
              NavigationRailDestination(
                icon: Icon(Icons.dashboard),
                label: Text(''),
              ),
              NavigationRailDestination(
                icon: Icon(Icons.inventory),
                label: Text(''),
              ),
              NavigationRailDestination(
                icon: Icon(Icons.shopping_cart),
                label: Text(''),
              ),
              NavigationRailDestination(
                icon: Icon(Icons.people),
                label: Text(''),
              ),
              NavigationRailDestination(
                icon: Icon(Icons.analytics),
                label: Text(''),
              ),
              NavigationRailDestination(
                icon: Icon(Icons.settings),
                label: Text(''),
              ),
            ],
          ),
          const VerticalDivider(thickness: 1, width: 1),
          Expanded(child: _pages[_selectedIndex]),
        ],
      ),
    );
  }
}

class DashboardOverview extends StatelessWidget {
  const DashboardOverview({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'نظرة عامة',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 20),
          Expanded(
            child: GridView.count(
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 1.4,
              children: [
                _buildStatCard(
                  'إجمالي الطلبات',
                  '1,234',
                  Icons.shopping_cart,
                  Colors.blue,
                ),
                _buildStatCard(
                  'إجمالي المبيعات',
                  '\$12,345',
                  Icons.attach_money,
                  Colors.green,
                ),
                _buildStatCard(
                  'عدد العملاء',
                  '567',
                  Icons.people,
                  Colors.orange,
                ),
                _buildStatCard(
                  'المنتجات',
                  '89',
                  Icons.inventory,
                  Colors.purple,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Flexible(child: Icon(icon, size: 32, color: color)),
            const SizedBox(height: 6),
            Flexible(
              child: FittedBox(
                fit: BoxFit.scaleDown,
                child: Text(
                  value,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 2),
            Flexible(
              child: FittedBox(
                fit: BoxFit.scaleDown,
                child: Text(
                  title,
                  style: const TextStyle(fontSize: 10),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class ProductsManagement extends StatefulWidget {
  const ProductsManagement({super.key});

  @override
  State<ProductsManagement> createState() => _ProductsManagementState();
}

class _ProductsManagementState extends State<ProductsManagement> {
  final List<Map<String, dynamic>> _products = [
    {
      'id': 1,
      'name': 'عدسات أكيوفيو أويسيس',
      'brand': 'Johnson & Johnson',
      'price': 150.0,
      'stock': 25,
      'category': 'يومية',
      'status': 'متوفر',
    },
    {
      'id': 2,
      'name': 'عدسات بايوفينيتي',
      'brand': 'CooperVision',
      'price': 120.0,
      'stock': 15,
      'category': 'شهرية',
      'status': 'متوفر',
    },
    {
      'id': 3,
      'name': 'عدسات أير أوبتيكس',
      'brand': 'Alcon',
      'price': 180.0,
      'stock': 8,
      'category': 'شهرية',
      'status': 'مخزون منخفض',
    },
    {
      'id': 4,
      'name': 'عدسات فريش لوك',
      'brand': 'Alcon',
      'price': 200.0,
      'stock': 0,
      'category': 'ملونة',
      'status': 'نفد المخزون',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'إدارة المنتجات',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              ElevatedButton.icon(
                onPressed: () => _showAddProductDialog(),
                icon: const Icon(Icons.add),
                label: const Text('إضافة منتج'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('المعرف')),
                  DataColumn(label: Text('اسم المنتج')),
                  DataColumn(label: Text('العلامة التجارية')),
                  DataColumn(label: Text('السعر')),
                  DataColumn(label: Text('المخزون')),
                  DataColumn(label: Text('الفئة')),
                  DataColumn(label: Text('الحالة')),
                  DataColumn(label: Text('الإجراءات')),
                ],
                rows: _products.map((product) {
                  return DataRow(
                    cells: [
                      DataCell(Text(product['id'].toString())),
                      DataCell(Text(product['name'])),
                      DataCell(Text(product['brand'])),
                      DataCell(Text('${product['price']} ر.س')),
                      DataCell(Text(product['stock'].toString())),
                      DataCell(Text(product['category'])),
                      DataCell(
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: _getStatusColor(product['status']),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            product['status'],
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ),
                      DataCell(
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IconButton(
                              icon: const Icon(Icons.edit, color: Colors.blue),
                              onPressed: () => _showEditProductDialog(product),
                            ),
                            IconButton(
                              icon: const Icon(Icons.delete, color: Colors.red),
                              onPressed: () => _deleteProduct(product['id']),
                            ),
                          ],
                        ),
                      ),
                    ],
                  );
                }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'متوفر':
        return Colors.green;
      case 'مخزون منخفض':
        return Colors.orange;
      case 'نفد المخزون':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  void _showAddProductDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إضافة منتج جديد'),
        content: const Text('سيتم إضافة نموذج إضافة المنتج هنا'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إضافة'),
          ),
        ],
      ),
    );
  }

  void _showEditProductDialog(Map<String, dynamic> product) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تعديل ${product['name']}'),
        content: const Text('سيتم إضافة نموذج تعديل المنتج هنا'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  void _deleteProduct(int id) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف المنتج'),
        content: const Text('هل أنت متأكد من حذف هذا المنتج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _products.removeWhere((product) => product['id'] == id);
              });
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم حذف المنتج بنجاح')),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}

class OrdersManagement extends StatefulWidget {
  const OrdersManagement({super.key});

  @override
  State<OrdersManagement> createState() => _OrdersManagementState();
}

class _OrdersManagementState extends State<OrdersManagement> {
  final List<Map<String, dynamic>> _orders = [
    {
      'id': '#ORD001',
      'customer': 'أحمد محمد',
      'product': 'عدسات أكيوفيو أويسيس',
      'quantity': 2,
      'total': 300.0,
      'status': 'جديد',
      'date': '2024-01-15',
    },
    {
      'id': '#ORD002',
      'customer': 'فاطمة علي',
      'product': 'عدسات بايوفينيتي',
      'quantity': 1,
      'total': 120.0,
      'status': 'قيد التحضير',
      'date': '2024-01-14',
    },
    {
      'id': '#ORD003',
      'customer': 'محمد سالم',
      'product': 'عدسات أير أوبتيكس',
      'quantity': 3,
      'total': 540.0,
      'status': 'تم الشحن',
      'date': '2024-01-13',
    },
    {
      'id': '#ORD004',
      'customer': 'نورا أحمد',
      'product': 'عدسات فريش لوك',
      'quantity': 1,
      'total': 200.0,
      'status': 'تم التسليم',
      'date': '2024-01-12',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'إدارة الطلبات',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 20),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildStatusFilter('الكل', null),
                const SizedBox(width: 8),
                _buildStatusFilter('جديد', 'جديد'),
                const SizedBox(width: 8),
                _buildStatusFilter('قيد التحضير', 'قيد التحضير'),
                const SizedBox(width: 8),
                _buildStatusFilter('تم الشحن', 'تم الشحن'),
                const SizedBox(width: 8),
                _buildStatusFilter('تم التسليم', 'تم التسليم'),
              ],
            ),
          ),
          const SizedBox(height: 20),
          Expanded(
            child: ListView.builder(
              itemCount: _orders.length,
              itemBuilder: (context, index) {
                final order = _orders[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 10),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: _getOrderStatusColor(order['status']),
                      child: Text(
                        order['id'].substring(4),
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    title: Text(order['customer']),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(order['product']),
                        Text(
                          'الكمية: ${order['quantity']} - التاريخ: ${order['date']}',
                        ),
                      ],
                    ),
                    trailing: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          '${order['total']} ر.س',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: _getOrderStatusColor(order['status']),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            order['status'],
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                            ),
                          ),
                        ),
                      ],
                    ),
                    onTap: () => _showOrderDetails(order),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusFilter(String label, String? status) {
    return FilterChip(
      label: Text(label),
      selected: false,
      onSelected: (selected) {
        // يمكن إضافة منطق الفلترة هنا
      },
    );
  }

  Color _getOrderStatusColor(String status) {
    switch (status) {
      case 'جديد':
        return Colors.blue;
      case 'قيد التحضير':
        return Colors.orange;
      case 'تم الشحن':
        return Colors.purple;
      case 'تم التسليم':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  void _showOrderDetails(Map<String, dynamic> order) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل الطلب ${order['id']}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('العميل: ${order['customer']}'),
            Text('المنتج: ${order['product']}'),
            Text('الكمية: ${order['quantity']}'),
            Text('المجموع: ${order['total']} ر.س'),
            Text('الحالة: ${order['status']}'),
            Text('التاريخ: ${order['date']}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _updateOrderStatus(order);
            },
            child: const Text('تحديث الحالة'),
          ),
        ],
      ),
    );
  }

  void _updateOrderStatus(Map<String, dynamic> order) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تحديث حالة الطلب'),
        content: const Text('سيتم إضافة خيارات تحديث الحالة هنا'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('تحديث'),
          ),
        ],
      ),
    );
  }
}

class CustomersManagement extends StatelessWidget {
  const CustomersManagement({super.key});

  final List<Map<String, dynamic>> customers = const [
    {
      'id': 1,
      'name': 'أحمد محمد',
      'email': '<EMAIL>',
      'phone': '+966501234567',
      'orders': 5,
      'totalSpent': 1250.0,
      'joinDate': '2023-12-01',
      'status': 'نشط',
    },
    {
      'id': 2,
      'name': 'فاطمة علي',
      'email': '<EMAIL>',
      'phone': '+966507654321',
      'orders': 3,
      'totalSpent': 780.0,
      'joinDate': '2024-01-05',
      'status': 'نشط',
    },
    {
      'id': 3,
      'name': 'محمد سالم',
      'email': '<EMAIL>',
      'phone': '+966509876543',
      'orders': 8,
      'totalSpent': 2100.0,
      'joinDate': '2023-11-15',
      'status': 'VIP',
    },
    {
      'id': 4,
      'name': 'نورا أحمد',
      'email': '<EMAIL>',
      'phone': '+966502468135',
      'orders': 1,
      'totalSpent': 200.0,
      'joinDate': '2024-01-10',
      'status': 'جديد',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'إدارة العملاء',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 20),
          Expanded(
            child: ListView.builder(
              itemCount: customers.length,
              itemBuilder: (context, index) {
                final customer = customers[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 10),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: _getCustomerStatusColor(
                        customer['status'],
                      ),
                      child: Text(
                        customer['name'].substring(0, 1),
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    title: Text(customer['name']),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(customer['email']),
                        Text(
                          '${customer['orders']} طلبات - ${customer['totalSpent']} ر.س',
                        ),
                      ],
                    ),
                    trailing: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: _getCustomerStatusColor(customer['status']),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Text(
                        customer['status'],
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                        ),
                      ),
                    ),
                    onTap: () => _showCustomerDetails(context, customer),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Color _getCustomerStatusColor(String status) {
    switch (status) {
      case 'VIP':
        return Colors.purple;
      case 'نشط':
        return Colors.green;
      case 'جديد':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  void _showCustomerDetails(
    BuildContext context,
    Map<String, dynamic> customer,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل العميل: ${customer['name']}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('البريد: ${customer['email']}'),
            Text('الهاتف: ${customer['phone']}'),
            Text('عدد الطلبات: ${customer['orders']}'),
            Text('إجمالي المشتريات: ${customer['totalSpent']} ر.س'),
            Text('تاريخ الانضمام: ${customer['joinDate']}'),
            Text('الحالة: ${customer['status']}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}

class ReportsPage extends StatelessWidget {
  const ReportsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'التقارير والإحصائيات',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 20),
          Expanded(
            child: GridView.count(
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 2.2,
              children: [
                _buildReportCard(
                  'مبيعات اليوم',
                  '2,450 ر.س',
                  Icons.today,
                  Colors.green,
                  '+12%',
                ),
                _buildReportCard(
                  'مبيعات الشهر',
                  '45,230 ر.س',
                  Icons.calendar_month,
                  Colors.blue,
                  '+8%',
                ),
                _buildReportCard(
                  'أكثر المنتجات مبيعاً',
                  'أكيوفيو أويسيس',
                  Icons.trending_up,
                  Colors.orange,
                  '45 قطعة',
                ),
                _buildReportCard(
                  'متوسط قيمة الطلب',
                  '185 ر.س',
                  Icons.analytics,
                  Colors.purple,
                  '+5%',
                ),
                _buildReportCard(
                  'عملاء جدد',
                  '23 عميل',
                  Icons.person_add,
                  Colors.teal,
                  'هذا الشهر',
                ),
                _buildReportCard(
                  'معدل الإرجاع',
                  '2.1%',
                  Icons.assignment_return,
                  Colors.red,
                  '-0.5%',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReportCard(
    String title,
    String value,
    IconData icon,
    Color color,
    String subtitle,
  ) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Icon(icon, size: 24, color: color),
            FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                value,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                title,
                style: const TextStyle(fontSize: 9),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                subtitle,
                style: TextStyle(fontSize: 7, color: Colors.grey[600]),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'الإعدادات',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 20),
          Expanded(
            child: ListView(
              children: [
                _buildSettingsSection('إعدادات المتجر', [
                  _buildSettingItem(Icons.store, 'اسم المتجر', 'VisionLens'),
                  _buildSettingItem(
                    Icons.email,
                    'البريد الإلكتروني',
                    '<EMAIL>',
                  ),
                  _buildSettingItem(Icons.phone, 'رقم الهاتف', '+966501234567'),
                  _buildSettingItem(
                    Icons.location_on,
                    'العنوان',
                    'الرياض، المملكة العربية السعودية',
                  ),
                ]),
                const SizedBox(height: 20),
                _buildSettingsSection('إعدادات الدفع', [
                  _buildSettingItem(
                    Icons.payment,
                    'طرق الدفع',
                    'فيزا، ماستركارد، مدى',
                  ),
                  _buildSettingItem(
                    Icons.local_shipping,
                    'رسوم الشحن',
                    '25 ر.س',
                  ),
                  _buildSettingItem(
                    Icons.money_off,
                    'الشحن المجاني',
                    'للطلبات أكثر من 200 ر.س',
                  ),
                ]),
                const SizedBox(height: 20),
                _buildSettingsSection('إعدادات الإشعارات', [
                  _buildSettingItem(
                    Icons.notifications,
                    'إشعارات الطلبات الجديدة',
                    'مفعل',
                  ),
                  _buildSettingItem(
                    Icons.inventory,
                    'تنبيهات المخزون المنخفض',
                    'مفعل',
                  ),
                  _buildSettingItem(
                    Icons.email_outlined,
                    'إشعارات البريد الإلكتروني',
                    'مفعل',
                  ),
                ]),
                const SizedBox(height: 20),
                _buildSettingsSection('إعدادات النظام', [
                  _buildSettingItem(Icons.backup, 'النسخ الاحتياطي', 'يومي'),
                  _buildSettingItem(Icons.security, 'الأمان', 'مفعل'),
                  _buildSettingItem(
                    Icons.update,
                    'التحديثات التلقائية',
                    'مفعل',
                  ),
                ]),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsSection(String title, List<Widget> items) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.blue,
          ),
        ),
        const SizedBox(height: 10),
        Card(child: Column(children: items)),
      ],
    );
  }

  Widget _buildSettingItem(IconData icon, String title, String value) {
    return ListTile(
      leading: Icon(icon, color: Colors.blue),
      title: Text(title),
      subtitle: Text(value),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: () {
        // يمكن إضافة منطق التعديل هنا
      },
    );
  }
}
