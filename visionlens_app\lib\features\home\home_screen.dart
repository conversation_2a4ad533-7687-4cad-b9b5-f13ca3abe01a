import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../shared/widgets/custom_app_bar.dart';
import '../../shared/widgets/category_card.dart';
import '../../shared/widgets/product_card.dart';
import '../../shared/widgets/banner_carousel.dart';
import '../../shared/widgets/section_header.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: const CustomAppBar(
        title: 'عدستي',
        showBackButton: false,
        showCartIcon: true,
        showSearchIcon: true,
      ),
      body: RefreshIndicator(
        onRefresh: _onRefresh,
        child: SingleChildScrollView(
          controller: _scrollController,
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Search Bar
              _buildSearchBar(),

              // Banner Carousel
              const BannerCarousel(),

              const SizedBox(height: 20),

              // Categories Section
              _buildCategoriesSection(),

              const SizedBox(height: 20),

              // Featured Products Section
              _buildFeaturedProductsSection(),

              const SizedBox(height: 20),

              // Best Sellers Section
              _buildBestSellersSection(),

              const SizedBox(height: 20),

              // Latest Products Section
              _buildLatestProductsSection(),

              const SizedBox(height: 20),

              // Tips Section
              _buildTipsSection(),

              const SizedBox(height: 100), // Bottom padding for navigation
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: TextField(
        decoration: InputDecoration(
          hintText: 'ابحث عن العدسات...',
          prefixIcon: const Icon(Icons.search, color: AppColors.textSecondary),
          suffixIcon: IconButton(
            icon: const Icon(Icons.tune, color: AppColors.textSecondary),
            onPressed: () {
              // TODO: Open filter dialog
            },
          ),
          filled: true,
          fillColor: AppColors.surface,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
        onTap: () {
          // TODO: Navigate to search screen
        },
        readOnly: true,
      ),
    );
  }

  Widget _buildCategoriesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SectionHeader(
          title: 'الفئات',
          onSeeAllPressed: () {
            // TODO: Navigate to categories screen
          },
        ),
        const SizedBox(height: 12),
        SizedBox(
          height: 120,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: _getDummyCategories().length,
            itemBuilder: (context, index) {
              final category = _getDummyCategories()[index];
              return Padding(
                padding: const EdgeInsets.only(left: 12),
                child: CategoryCard(
                  title: category['name'],
                  icon: category['icon'],
                  color: category['color'],
                  onTap: () {
                    // TODO: Navigate to category products
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildFeaturedProductsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SectionHeader(
          title: 'المنتجات المميزة',
          onSeeAllPressed: () {
            // TODO: Navigate to featured products
          },
        ),
        const SizedBox(height: 12),
        SizedBox(
          height: 300,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: _getDummyProducts().length,
            itemBuilder: (context, index) {
              final product = _getDummyProducts()[index];
              return Padding(
                padding: const EdgeInsets.only(left: 12),
                child: SizedBox(
                  width: 180,
                  child: ProductCard(
                    imageUrl: product['image'],
                    name: product['name'],
                    price: product['price'],
                    originalPrice: product['originalPrice'],
                    rating: product['rating'],
                    isInStock: product['isInStock'],
                    onTap: () {
                      // TODO: Navigate to product details
                    },
                    onAddToCart: () {
                      // TODO: Add to cart
                    },
                    onToggleFavorite: () {
                      // TODO: Toggle favorite
                    },
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildBestSellersSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SectionHeader(
          title: 'الأكثر مبيعاً',
          onSeeAllPressed: () {
            // TODO: Navigate to best sellers
          },
        ),
        const SizedBox(height: 12),
        SizedBox(
          height: 300,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: _getDummyProducts().length,
            itemBuilder: (context, index) {
              final product = _getDummyProducts()[index];
              return Padding(
                padding: const EdgeInsets.only(left: 12),
                child: SizedBox(
                  width: 180,
                  child: ProductCard(
                    imageUrl: product['image'],
                    name: product['name'],
                    price: product['price'],
                    originalPrice: product['originalPrice'],
                    rating: product['rating'],
                    isInStock: product['isInStock'],
                    showBestSellerBadge: true,
                    onTap: () {
                      // TODO: Navigate to product details
                    },
                    onAddToCart: () {
                      // TODO: Add to cart
                    },
                    onToggleFavorite: () {
                      // TODO: Toggle favorite
                    },
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildLatestProductsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SectionHeader(
          title: 'أحدث المنتجات',
          onSeeAllPressed: () {
            // TODO: Navigate to latest products
          },
        ),
        const SizedBox(height: 12),
        SizedBox(
          height: 300,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: _getDummyProducts().length,
            itemBuilder: (context, index) {
              final product = _getDummyProducts()[index];
              return Padding(
                padding: const EdgeInsets.only(left: 12),
                child: SizedBox(
                  width: 180,
                  child: ProductCard(
                    imageUrl: product['image'],
                    name: product['name'],
                    price: product['price'],
                    originalPrice: product['originalPrice'],
                    rating: product['rating'],
                    isInStock: product['isInStock'],
                    showNewBadge: true,
                    onTap: () {
                      // TODO: Navigate to product details
                    },
                    onAddToCart: () {
                      // TODO: Add to cart
                    },
                    onToggleFavorite: () {
                      // TODO: Toggle favorite
                    },
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildTipsSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: AppColors.primaryGradient,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.lightbulb_outline,
                color: Colors.white,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'نصائح العناية بالعدسات',
                style: AppTextStyles.titleMedium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'اغسل يديك جيداً قبل لمس العدسات واحرص على تنظيفها يومياً بالمحلول المخصص.',
            style: AppTextStyles.bodyMedium.copyWith(
              color: Colors.white.withOpacity(0.9),
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              // TODO: Navigate to tips page
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              foregroundColor: AppColors.primary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('المزيد من النصائح'),
          ),
        ],
      ),
    );
  }

  Future<void> _onRefresh() async {
    // TODO: Refresh data
    await Future.delayed(const Duration(seconds: 1));
  }

  List<Map<String, dynamic>> _getDummyCategories() {
    return [
      {
        'name': 'يومية',
        'icon': Icons.calendar_today,
        'color': AppColors.categoryColors[0],
      },
      {
        'name': 'شهرية',
        'icon': Icons.calendar_month,
        'color': AppColors.categoryColors[1],
      },
      {
        'name': 'ملونة',
        'icon': Icons.palette,
        'color': AppColors.categoryColors[2],
      },
      {
        'name': 'طبية',
        'icon': Icons.medical_services,
        'color': AppColors.categoryColors[3],
      },
      {
        'name': 'تجميلية',
        'icon': Icons.auto_awesome,
        'color': AppColors.categoryColors[4],
      },
    ];
  }

  List<Map<String, dynamic>> _getDummyProducts() {
    return [
      {
        'name': 'عدسات أكيوفيو يومية',
        'price': 25000.0,
        'originalPrice': 30000.0,
        'rating': 4.5,
        'image': 'https://via.placeholder.com/200x200',
        'isInStock': true,
      },
      {
        'name': 'عدسات بايوفينيتي شهرية',
        'price': 45000.0,
        'originalPrice': null,
        'rating': 4.8,
        'image': 'https://via.placeholder.com/200x200',
        'isInStock': true,
      },
      {
        'name': 'عدسات فريش لوك ملونة',
        'price': 35000.0,
        'originalPrice': 40000.0,
        'rating': 4.3,
        'image': 'https://via.placeholder.com/200x200',
        'isInStock': false,
      },
    ];
  }
}
