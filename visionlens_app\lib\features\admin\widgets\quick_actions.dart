import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';

class QuickActions extends StatelessWidget {
  const QuickActions({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Text(
              'إجراءات سريعة',
              style: AppTextStyles.headlineSmall.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Quick Action Buttons
            _buildQuickAction(
              icon: Icons.add_box_outlined,
              title: 'إضافة منتج جديد',
              subtitle: 'أضف منتج جديد للمتجر',
              color: AppColors.primary,
              onTap: () {
                // TODO: Navigate to add product
              },
            ),
            
            const SizedBox(height: 12),
            
            _buildQuickAction(
              icon: Icons.local_offer_outlined,
              title: 'إنشاء عرض جديد',
              subtitle: 'أنشئ عرض أو خصم جديد',
              color: AppColors.success,
              onTap: () {
                // TODO: Navigate to create offer
              },
            ),
            
            const SizedBox(height: 12),
            
            _buildQuickAction(
              icon: Icons.category_outlined,
              title: 'إدارة الفئات',
              subtitle: 'أضف أو عدل فئات المنتجات',
              color: AppColors.info,
              onTap: () {
                // TODO: Navigate to manage categories
              },
            ),
            
            const SizedBox(height: 12),
            
            _buildQuickAction(
              icon: Icons.analytics_outlined,
              title: 'عرض التقارير',
              subtitle: 'تقارير المبيعات والإحصائيات',
              color: AppColors.warning,
              onTap: () {
                // TODO: Navigate to reports
              },
            ),
            
            const SizedBox(height: 20),
            
            // Recent Activity
            _buildRecentActivity(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickAction({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: color.withValues(alpha: 0.2)),
          ),
          child: Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 20,
                ),
              ),
              
              const SizedBox(width: 12),
              
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: AppTextStyles.labelMedium.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      subtitle,
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              
              Icon(
                Icons.chevron_right,
                color: AppColors.textSecondary,
                size: 20,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRecentActivity() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'النشاط الأخير',
          style: AppTextStyles.labelLarge.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        
        const SizedBox(height: 12),
        
        _buildActivityItem(
          icon: Icons.shopping_cart,
          text: 'طلب جديد من أحمد محمد',
          time: 'منذ 5 دقائق',
          color: AppColors.primary,
        ),
        
        _buildActivityItem(
          icon: Icons.inventory_2,
          text: 'تم إضافة منتج جديد',
          time: 'منذ 30 دقيقة',
          color: AppColors.success,
        ),
        
        _buildActivityItem(
          icon: Icons.person_add,
          text: 'عميل جديد انضم للمتجر',
          time: 'منذ ساعة',
          color: AppColors.info,
        ),
        
        _buildActivityItem(
          icon: Icons.local_shipping,
          text: 'تم شحن 3 طلبات',
          time: 'منذ ساعتين',
          color: AppColors.warning,
        ),
      ],
    );
  }

  Widget _buildActivityItem({
    required IconData icon,
    required String text,
    required String time,
    required Color color,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: color,
              size: 16,
            ),
          ),
          
          const SizedBox(width: 12),
          
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  text,
                  style: AppTextStyles.bodySmall.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  time,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                    fontSize: 11,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
