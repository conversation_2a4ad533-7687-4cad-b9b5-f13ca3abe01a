// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Product _$ProductFromJson(Map<String, dynamic> json) => Product(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      description: json['description'] as String,
      price: (json['price'] as num).toDouble(),
      discountPrice: (json['discount_price'] as num?)?.toDouble(),
      category: Category.fromJson(json['category'] as Map<String, dynamic>),
      brand: Brand.fromJson(json['brand'] as Map<String, dynamic>),
      lensType: json['lens_type'] as String,
      lensUsage: json['lens_usage'] as String,
      powerRange: json['power_range'] as String?,
      diameter: (json['diameter'] as num?)?.toDouble(),
      baseCurve: (json['base_curve'] as num?)?.toDouble(),
      waterContent: (json['water_content'] as num?)?.toDouble(),
      material: json['material'] as String?,
      color: json['color'] as String?,
      stockQuantity: (json['stock_quantity'] as num).toInt(),
      isActive: json['is_active'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      image: json['image'] as String?,
      images:
          (json['images'] as List<dynamic>?)?.map((e) => e as String).toList(),
      rating: (json['rating'] as num?)?.toDouble(),
      reviewCount: (json['review_count'] as num?)?.toInt() ?? 0,
      isFeatured: json['is_featured'] as bool? ?? false,
      isBestseller: json['is_bestseller'] as bool? ?? false,
    );

Map<String, dynamic> _$ProductToJson(Product instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'price': instance.price,
      'discount_price': instance.discountPrice,
      'category': instance.category,
      'brand': instance.brand,
      'lens_type': instance.lensType,
      'lens_usage': instance.lensUsage,
      'power_range': instance.powerRange,
      'diameter': instance.diameter,
      'base_curve': instance.baseCurve,
      'water_content': instance.waterContent,
      'material': instance.material,
      'color': instance.color,
      'stock_quantity': instance.stockQuantity,
      'is_active': instance.isActive,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
      'image': instance.image,
      'images': instance.images,
      'rating': instance.rating,
      'review_count': instance.reviewCount,
      'is_featured': instance.isFeatured,
      'is_bestseller': instance.isBestseller,
    };

Category _$CategoryFromJson(Map<String, dynamic> json) => Category(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      description: json['description'] as String,
      image: json['image'] as String?,
      isActive: json['is_active'] as bool,
      sortOrder: (json['sort_order'] as num).toInt(),
      productCount: (json['product_count'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$CategoryToJson(Category instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'image': instance.image,
      'is_active': instance.isActive,
      'sort_order': instance.sortOrder,
      'product_count': instance.productCount,
    };

Brand _$BrandFromJson(Map<String, dynamic> json) => Brand(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      description: json['description'] as String,
      logo: json['logo'] as String?,
      website: json['website'] as String?,
      isActive: json['is_active'] as bool,
      productCount: (json['product_count'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$BrandToJson(Brand instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'logo': instance.logo,
      'website': instance.website,
      'is_active': instance.isActive,
      'product_count': instance.productCount,
    };
