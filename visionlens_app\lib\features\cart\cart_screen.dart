import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/constants/app_constants.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../core/providers/cart_provider.dart';
import '../../shared/widgets/custom_app_bar.dart';
import '../checkout/checkout_screen.dart';
import 'widgets/cart_item_card.dart';
import 'widgets/cart_summary.dart';
import 'widgets/empty_cart.dart';

class CartScreen extends ConsumerStatefulWidget {
  const CartScreen({super.key});

  @override
  ConsumerState<CartScreen> createState() => _CartScreenState();
}

class _CartScreenState extends ConsumerState<CartScreen> {
  @override
  Widget build(BuildContext context) {
    final cartState = ref.watch(cartProvider);

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: CustomAppBar(
        title: 'سلة التسوق',
        showBackButton: false,
        actions: [
          if (cartState.isNotEmpty)
            TextButton(
              onPressed: _showClearCartDialog,
              child: Text(
                'مسح الكل',
                style: AppTextStyles.labelMedium.copyWith(
                  color: AppColors.error,
                ),
              ),
            ),
        ],
      ),
      body: cartState.isEmpty
          ? _buildEmptyCart()
          : _buildCartContent(cartState),
      bottomNavigationBar: cartState.isNotEmpty
          ? _buildBottomBar(cartState)
          : null,
    );
  }

  Widget _buildEmptyCart() {
    return const EmptyCart();
  }

  Widget _buildCartContent(cartState) {
    return Column(
      children: [
        // Cart Items List
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: cartState.items.length,
            itemBuilder: (context, index) {
              final item = cartState.items[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: CartItemCard(
                  imageUrl: item.product.imageUrl,
                  name: item.product.name,
                  price: item.product.price,
                  originalPrice: item.product.originalPrice,
                  quantity: item.quantity,
                  isInStock: item.product.isInStock,
                  onQuantityChanged: (newQuantity) {
                    _updateQuantity(item.id, newQuantity);
                  },
                  onRemove: () {
                    _removeItem(item.id);
                  },
                ),
              );
            },
          ),
        ),

        // Cart Summary
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.surface,
            boxShadow: [
              BoxShadow(
                color: AppColors.shadow,
                blurRadius: 8,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: CartSummary(
            subtotal: cartState.subtotal,
            shipping: cartState.shipping,
            tax: cartState.tax,
            total: cartState.total,
            itemCount: cartState.totalItems,
          ),
        ),
      ],
    );
  }

  Widget _buildBottomBar(cartState) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Total
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'الإجمالي:',
                  style: AppTextStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${cartState.total.toStringAsFixed(0)} ${AppConstants.currencySymbol}',
                  style: AppTextStyles.titleMedium.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Checkout Button
            SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton(
                onPressed: _proceedToCheckout,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.shopping_bag),
                    const SizedBox(width: 8),
                    Text(
                      'متابعة الدفع (${cartState.totalItems} عنصر)',
                      style: AppTextStyles.buttonPrimary,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _updateQuantity(String itemId, int newQuantity) {
    ref.read(cartProvider.notifier).updateQuantity(itemId, newQuantity);
  }

  void _removeItem(String itemId) {
    ref.read(cartProvider.notifier).removeItem(itemId);

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم حذف المنتج من السلة'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _showClearCartDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('مسح السلة'),
          content: const Text(
            'هل أنت متأكد من رغبتك في مسح جميع المنتجات من السلة؟',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _clearCart();
              },
              style: TextButton.styleFrom(foregroundColor: AppColors.error),
              child: const Text('مسح'),
            ),
          ],
        );
      },
    );
  }

  void _clearCart() {
    ref.read(cartProvider.notifier).clearCart();

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم مسح السلة بنجاح'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _proceedToCheckout() {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const CheckoutScreen()));
  }
}
