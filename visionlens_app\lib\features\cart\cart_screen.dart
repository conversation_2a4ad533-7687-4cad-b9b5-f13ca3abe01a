import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/constants/app_constants.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../shared/widgets/custom_app_bar.dart';
import 'widgets/cart_item_card.dart';
import 'widgets/cart_summary.dart';
import 'widgets/empty_cart.dart';

class CartScreen extends ConsumerStatefulWidget {
  const CartScreen({super.key});

  @override
  ConsumerState<CartScreen> createState() => _CartScreenState();
}

class _CartScreenState extends ConsumerState<CartScreen> {
  List<Map<String, dynamic>> _cartItems = [];

  @override
  void initState() {
    super.initState();
    _loadCartItems();
  }

  void _loadCartItems() {
    // TODO: Load cart items from provider/storage
    setState(() {
      _cartItems = _getDummyCartItems();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: CustomAppBar(
        title: 'سلة التسوق',
        showBackButton: false,
        actions: [
          if (_cartItems.isNotEmpty)
            TextButton(
              onPressed: _showClearCartDialog,
              child: Text(
                'مسح الكل',
                style: AppTextStyles.labelMedium.copyWith(
                  color: AppColors.error,
                ),
              ),
            ),
        ],
      ),
      body: _cartItems.isEmpty ? _buildEmptyCart() : _buildCartContent(),
      bottomNavigationBar: _cartItems.isNotEmpty ? _buildBottomBar() : null,
    );
  }

  Widget _buildEmptyCart() {
    return const EmptyCart();
  }

  Widget _buildCartContent() {
    return Column(
      children: [
        // Cart Items List
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: _cartItems.length,
            itemBuilder: (context, index) {
              final item = _cartItems[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: CartItemCard(
                  imageUrl: item['image'],
                  name: item['name'],
                  price: item['price'],
                  originalPrice: item['originalPrice'],
                  quantity: item['quantity'],
                  isInStock: item['isInStock'],
                  onQuantityChanged: (newQuantity) {
                    _updateQuantity(index, newQuantity);
                  },
                  onRemove: () {
                    _removeItem(index);
                  },
                ),
              );
            },
          ),
        ),
        
        // Cart Summary
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.surface,
            boxShadow: [
              BoxShadow(
                color: AppColors.shadow,
                blurRadius: 8,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: CartSummary(
            subtotal: _calculateSubtotal(),
            shipping: _calculateShipping(),
            tax: _calculateTax(),
            total: _calculateTotal(),
            itemCount: _getTotalItems(),
          ),
        ),
      ],
    );
  }

  Widget _buildBottomBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Total
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'الإجمالي:',
                  style: AppTextStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${_calculateTotal().toStringAsFixed(0)} ${AppConstants.currencySymbol}',
                  style: AppTextStyles.titleMedium.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Checkout Button
            SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton(
                onPressed: _proceedToCheckout,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.shopping_bag),
                    const SizedBox(width: 8),
                    Text(
                      'متابعة الدفع (${_getTotalItems()} عنصر)',
                      style: AppTextStyles.buttonPrimary,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _updateQuantity(int index, int newQuantity) {
    if (newQuantity <= 0) {
      _removeItem(index);
      return;
    }
    
    setState(() {
      _cartItems[index]['quantity'] = newQuantity;
    });
    
    // TODO: Update quantity in provider/storage
  }

  void _removeItem(int index) {
    setState(() {
      _cartItems.removeAt(index);
    });
    
    // TODO: Remove item from provider/storage
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم حذف المنتج من السلة'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _showClearCartDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('مسح السلة'),
          content: const Text('هل أنت متأكد من رغبتك في مسح جميع المنتجات من السلة؟'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _clearCart();
              },
              style: TextButton.styleFrom(
                foregroundColor: AppColors.error,
              ),
              child: const Text('مسح'),
            ),
          ],
        );
      },
    );
  }

  void _clearCart() {
    setState(() {
      _cartItems.clear();
    });
    
    // TODO: Clear cart in provider/storage
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم مسح السلة بنجاح'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _proceedToCheckout() {
    // TODO: Navigate to checkout screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('الانتقال إلى صفحة الدفع...'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  double _calculateSubtotal() {
    return _cartItems.fold(0.0, (sum, item) {
      return sum + (item['price'] * item['quantity']);
    });
  }

  double _calculateShipping() {
    final subtotal = _calculateSubtotal();
    return subtotal >= AppConstants.freeShippingThreshold 
        ? 0.0 
        : AppConstants.defaultShippingCost;
  }

  double _calculateTax() {
    return _calculateSubtotal() * 0.0; // No tax for now
  }

  double _calculateTotal() {
    return _calculateSubtotal() + _calculateShipping() + _calculateTax();
  }

  int _getTotalItems() {
    return _cartItems.fold(0, (sum, item) => sum + (item['quantity'] as int));
  }

  List<Map<String, dynamic>> _getDummyCartItems() {
    return [
      {
        'name': 'عدسات أكيوفيو يومية',
        'price': 25000.0,
        'originalPrice': 30000.0,
        'quantity': 2,
        'image': 'https://via.placeholder.com/100x100',
        'isInStock': true,
      },
      {
        'name': 'عدسات بايوفينيتي شهرية',
        'price': 45000.0,
        'originalPrice': null,
        'quantity': 1,
        'image': 'https://via.placeholder.com/100x100',
        'isInStock': true,
      },
      {
        'name': 'عدسات فريش لوك ملونة',
        'price': 35000.0,
        'originalPrice': 40000.0,
        'quantity': 1,
        'image': 'https://via.placeholder.com/100x100',
        'isInStock': false,
      },
    ];
  }
}
