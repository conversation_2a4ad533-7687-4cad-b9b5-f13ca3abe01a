import 'package:flutter/material.dart';

class AppColors {
  // Primary Colors
  static const Color primary = Color(0xFF2E86AB); // أزرق العدسات
  static const Color primaryLight = Color(0xFF5BA3C7);
  static const Color primaryDark = Color(0xFF1E5F7F);
  
  // Secondary Colors
  static const Color secondary = Color(0xFFA23B72); // وردي أنيق
  static const Color secondaryLight = Color(0xFFB85A8A);
  static const Color secondaryDark = Color(0xFF7A2B55);
  
  // Accent Colors
  static const Color accent = Color(0xFFF18F01); // برتقالي دافئ
  static const Color accentLight = Color(0xFFF4A533);
  static const Color accentDark = Color(0xFFD67A00);
  
  // Background Colors
  static const Color background = Color(0xFFF5F5F5); // رمادي فاتح
  static const Color surface = Color(0xFFFFFFFF); // أبيض
  static const Color surfaceVariant = Color(0xFFF8F9FA);
  
  // Text Colors
  static const Color textPrimary = Color(0xFF2C3E50); // رمادي داكن
  static const Color textSecondary = Color(0xFF7F8C8D); // رمادي متوسط
  static const Color textLight = Color(0xFFBDC3C7); // رمادي فاتح
  static const Color textOnPrimary = Color(0xFFFFFFFF);
  static const Color textOnSecondary = Color(0xFFFFFFFF);
  
  // Status Colors
  static const Color success = Color(0xFF27AE60); // أخضر النجاح
  static const Color successLight = Color(0xFF52C882);
  static const Color successDark = Color(0xFF1E8449);
  
  static const Color error = Color(0xFFE74C3C); // أحمر الخطأ
  static const Color errorLight = Color(0xFFEC7063);
  static const Color errorDark = Color(0xFFCB4335);
  
  static const Color warning = Color(0xFFF39C12); // برتقالي التحذير
  static const Color warningLight = Color(0xFFF5B041);
  static const Color warningDark = Color(0xFFD68910);
  
  static const Color info = Color(0xFF3498DB); // أزرق المعلومات
  static const Color infoLight = Color(0xFF5DADE2);
  static const Color infoDark = Color(0xFF2E86C1);
  
  // Border Colors
  static const Color border = Color(0xFFE1E8ED);
  static const Color borderLight = Color(0xFFF1F3F4);
  static const Color borderDark = Color(0xFFD5DBDB);
  
  // Shadow Colors
  static const Color shadow = Color(0x1A000000);
  static const Color shadowLight = Color(0x0D000000);
  static const Color shadowDark = Color(0x26000000);
  
  // Overlay Colors
  static const Color overlay = Color(0x80000000);
  static const Color overlayLight = Color(0x40000000);
  static const Color overlayDark = Color(0xB3000000);
  
  // Gradient Colors
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primary, primaryDark],
  );
  
  static const LinearGradient secondaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [secondary, secondaryDark],
  );
  
  static const LinearGradient accentGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [accent, accentDark],
  );
  
  static const LinearGradient successGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [success, successDark],
  );
  
  // Special Colors for E-commerce
  static const Color price = Color(0xFF27AE60); // أخضر السعر
  static const Color discount = Color(0xFFE74C3C); // أحمر الخصم
  static const Color outOfStock = Color(0xFF95A5A6); // رمادي نفاد المخزون
  static const Color inStock = Color(0xFF27AE60); // أخضر متوفر
  static const Color lowStock = Color(0xFFF39C12); // برتقالي مخزون قليل
  
  // Rating Colors
  static const Color ratingFilled = Color(0xFFFFC107); // أصفر النجوم
  static const Color ratingEmpty = Color(0xFFE0E0E0); // رمادي النجوم الفارغة
  
  // Cart Colors
  static const Color cartBadge = Color(0xFFE74C3C); // أحمر شارة السلة
  static const Color addToCart = Color(0xFF27AE60); // أخضر إضافة للسلة
  
  // Category Colors (for different lens categories)
  static const List<Color> categoryColors = [
    Color(0xFF3498DB), // أزرق
    Color(0xFF9B59B6), // بنفسجي
    Color(0xFFE67E22), // برتقالي
    Color(0xFF1ABC9C), // تركوازي
    Color(0xFFE74C3C), // أحمر
    Color(0xFFF39C12), // أصفر برتقالي
    Color(0xFF34495E), // رمادي داكن
    Color(0xFF16A085), // أخضر مزرق
  ];
  
  // Dark Theme Colors
  static const Color darkBackground = Color(0xFF121212);
  static const Color darkSurface = Color(0xFF1E1E1E);
  static const Color darkSurfaceVariant = Color(0xFF2C2C2C);
  static const Color darkTextPrimary = Color(0xFFFFFFFF);
  static const Color darkTextSecondary = Color(0xFFB3B3B3);
  static const Color darkBorder = Color(0xFF404040);
  
  // Shimmer Colors
  static const Color shimmerBase = Color(0xFFE0E0E0);
  static const Color shimmerHighlight = Color(0xFFF5F5F5);
  static const Color shimmerBaseDark = Color(0xFF424242);
  static const Color shimmerHighlightDark = Color(0xFF616161);
  
  // Helper Methods
  static Color withOpacity(Color color, double opacity) {
    return color.withOpacity(opacity);
  }
  
  static Color lighten(Color color, [double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(color);
    final hslLight = hsl.withLightness((hsl.lightness + amount).clamp(0.0, 1.0));
    return hslLight.toColor();
  }
  
  static Color darken(Color color, [double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(color);
    final hslDark = hsl.withLightness((hsl.lightness - amount).clamp(0.0, 1.0));
    return hslDark.toColor();
  }
}
