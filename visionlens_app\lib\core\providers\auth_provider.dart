import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';
import '../constants/app_constants.dart';
import '../../shared/models/user_model.dart';

// Auth State
class AuthState {
  final User? user;
  final bool isLoading;
  final String? error;
  final bool isLoggedIn;

  const AuthState({
    this.user,
    this.isLoading = false,
    this.error,
    this.isLoggedIn = false,
  });

  AuthState copyWith({
    User? user,
    bool? isLoading,
    String? error,
    bool? isLoggedIn,
  }) {
    return AuthState(
      user: user ?? this.user,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      isLoggedIn: isLoggedIn ?? this.isLoggedIn,
    );
  }
}

// Auth Notifier
class AuthNotifier extends StateNotifier<AuthState> {
  AuthNotifier() : super(const AuthState()) {
    _loadUserFromStorage();
  }

  // Load user from local storage
  Future<void> _loadUserFromStorage() async {
    try {
      final box = await Hive.openBox('auth');
      final userData = box.get(AppConstants.userDataKey);
      final token = box.get(AppConstants.userTokenKey);

      if (userData != null && token != null) {
        final user = User.fromJson(Map<String, dynamic>.from(userData));
        state = state.copyWith(user: user, isLoggedIn: true);
      }
    } catch (e) {
      print('Error loading user from storage: $e');
    }
  }

  // Login
  Future<bool> login(String email, String password) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // Mock user data - في التطبيق الحقيقي، ستأتي من API
      final user = User(
        id: 1,
        username: email.split('@')[0],
        firstName: 'أحمد',
        lastName: 'محمد',
        email: email,
        isActive: true,
        dateJoined: DateTime.now(),
        profile: const UserProfile(phone: '+964 ************'),
      );

      // Save to local storage
      final box = await Hive.openBox('auth');
      await box.put(AppConstants.userDataKey, user.toJson());
      await box.put(AppConstants.userTokenKey, 'mock_token_123456');

      state = state.copyWith(user: user, isLoggedIn: true, isLoading: false);

      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'خطأ في تسجيل الدخول: ${e.toString()}',
      );
      return false;
    }
  }

  // Register
  Future<bool> register({
    required String firstName,
    required String lastName,
    required String email,
    required String phone,
    required String password,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // Mock user creation
      final user = User(
        id: DateTime.now().millisecondsSinceEpoch,
        username: email.split('@')[0],
        firstName: firstName,
        lastName: lastName,
        email: email,
        isActive: true,
        dateJoined: DateTime.now(),
        profile: UserProfile(phone: phone),
      );

      // Save to local storage
      final box = await Hive.openBox('auth');
      await box.put(AppConstants.userDataKey, user.toJson());
      await box.put(AppConstants.userTokenKey, 'mock_token_${user.id}');

      state = state.copyWith(user: user, isLoggedIn: true, isLoading: false);

      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'خطأ في إنشاء الحساب: ${e.toString()}',
      );
      return false;
    }
  }

  // Logout
  Future<void> logout() async {
    try {
      final box = await Hive.openBox('auth');
      await box.delete(AppConstants.userDataKey);
      await box.delete(AppConstants.userTokenKey);

      state = const AuthState();
    } catch (e) {
      print('Error during logout: $e');
    }
  }

  // Update user profile
  Future<bool> updateProfile({
    String? firstName,
    String? lastName,
    String? phone,
  }) async {
    if (state.user == null) return false;

    state = state.copyWith(isLoading: true, error: null);

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      final updatedProfile = state.user!.profile?.copyWith(
        phone: phone ?? state.user!.profile?.phone,
      );

      final updatedUser = state.user!.copyWith(
        firstName: firstName ?? state.user!.firstName,
        lastName: lastName ?? state.user!.lastName,
        profile: updatedProfile,
      );

      // Save to local storage
      final box = await Hive.openBox('auth');
      await box.put(AppConstants.userDataKey, updatedUser.toJson());

      state = state.copyWith(user: updatedUser, isLoading: false);

      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'خطأ في تحديث الملف الشخصي: ${e.toString()}',
      );
      return false;
    }
  }

  // Change password
  Future<bool> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      // في التطبيق الحقيقي، ستتم مقارنة كلمة المرور الحالية
      state = state.copyWith(isLoading: false);
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'خطأ في تغيير كلمة المرور: ${e.toString()}',
      );
      return false;
    }
  }

  // Forgot password
  Future<bool> forgotPassword(String email) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      state = state.copyWith(isLoading: false);
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'خطأ في إرسال رابط استعادة كلمة المرور: ${e.toString()}',
      );
      return false;
    }
  }

  // Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Provider
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  return AuthNotifier();
});

// Helper providers
final isLoggedInProvider = Provider<bool>((ref) {
  return ref.watch(authProvider).isLoggedIn;
});

final currentUserProvider = Provider<User?>((ref) {
  return ref.watch(authProvider).user;
});

final authLoadingProvider = Provider<bool>((ref) {
  return ref.watch(authProvider).isLoading;
});

final authErrorProvider = Provider<String?>((ref) {
  return ref.watch(authProvider).error;
});
