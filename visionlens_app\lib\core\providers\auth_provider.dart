import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';
import '../constants/app_constants.dart';
import '../services/mock_auth_service.dart';
import '../../shared/models/user_model.dart';

// Auth State
class AuthState {
  final User? user;
  final bool isLoading;
  final String? error;
  final bool isLoggedIn;

  const AuthState({
    this.user,
    this.isLoading = false,
    this.error,
    this.isLoggedIn = false,
  });

  AuthState copyWith({
    User? user,
    bool? isLoading,
    String? error,
    bool? isLoggedIn,
  }) {
    return AuthState(
      user: user ?? this.user,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      isLoggedIn: isLoggedIn ?? this.isLoggedIn,
    );
  }
}

// Auth Notifier
class AuthNotifier extends StateNotifier<AuthState> {
  final MockAuthService _authService = MockAuthService();

  AuthNotifier() : super(const AuthState()) {
    _loadUserFromStorage();
    _listenToAuthChanges();
  }

  // Listen to Firebase auth changes
  void _listenToAuthChanges() {
    _authService.authStateChanges.listen((user) {
      state = state.copyWith(user: user, isLoggedIn: user != null);

      // Save to local storage
      if (user != null) {
        _saveUserToStorage(user);
      } else {
        _clearUserFromStorage();
      }
    });
  }

  // Load user from local storage
  Future<void> _loadUserFromStorage() async {
    try {
      final box = await Hive.openBox('auth');
      final userData = box.get(AppConstants.userDataKey);

      if (userData != null) {
        final user = User.fromJson(Map<String, dynamic>.from(userData));
        state = state.copyWith(user: user, isLoggedIn: true);
      }
    } catch (e) {
      // Handle error silently
    }
  }

  // Save user to local storage
  Future<void> _saveUserToStorage(User user) async {
    try {
      final box = await Hive.openBox('auth');
      await box.put(AppConstants.userDataKey, user.toJson());
    } catch (e) {
      // Handle error silently
    }
  }

  // Clear user from local storage
  Future<void> _clearUserFromStorage() async {
    try {
      final box = await Hive.openBox('auth');
      await box.delete(AppConstants.userDataKey);
    } catch (e) {
      // Handle error silently
    }
  }

  // Login with Email & Password
  Future<bool> login(String email, String password) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final user = await _authService.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (user != null) {
        // User will be automatically updated via _listenToAuthChanges
        state = state.copyWith(isLoading: false);
        return true;
      } else {
        state = state.copyWith(isLoading: false, error: 'فشل في تسجيل الدخول');
        return false;
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return false;
    }
  }

  // Register
  Future<bool> register({
    required String firstName,
    required String lastName,
    required String email,
    required String phone,
    required String password,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final user = await _authService.createUserWithEmailAndPassword(
        email: email,
        password: password,
        firstName: firstName,
        lastName: lastName,
        phone: phone,
      );

      if (user != null) {
        // User will be automatically updated via _listenToAuthChanges
        state = state.copyWith(isLoading: false);
        return true;
      } else {
        state = state.copyWith(isLoading: false, error: 'فشل في إنشاء الحساب');
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'خطأ في إنشاء الحساب: ${e.toString()}',
      );
      return false;
    }
  }

  // Google Sign In
  Future<bool> signInWithGoogle() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final user = await _authService.signInWithGoogle();

      if (user != null) {
        // User will be automatically updated via _listenToAuthChanges
        state = state.copyWith(isLoading: false);
        return true;
      } else {
        // User canceled the sign-in
        state = state.copyWith(isLoading: false);
        return false;
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return false;
    }
  }

  // Facebook Sign In
  Future<bool> signInWithFacebook() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final user = await _authService.signInWithFacebook();

      if (user != null) {
        // User will be automatically updated via _listenToAuthChanges
        state = state.copyWith(isLoading: false);
        return true;
      } else {
        // User canceled the sign-in
        state = state.copyWith(isLoading: false);
        return false;
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return false;
    }
  }

  // Apple Sign In
  Future<bool> signInWithApple() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final user = await _authService.signInWithApple();

      if (user != null) {
        // User will be automatically updated via _listenToAuthChanges
        state = state.copyWith(isLoading: false);
        return true;
      } else {
        // User canceled the sign-in
        state = state.copyWith(isLoading: false);
        return false;
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return false;
    }
  }

  // Logout
  Future<void> logout() async {
    try {
      await _authService.signOut();
      state = const AuthState();
    } catch (e) {
      // Handle error silently
    }
  }

  // Update user profile
  Future<bool> updateProfile({
    String? firstName,
    String? lastName,
    String? phone,
  }) async {
    if (state.user == null) return false;

    state = state.copyWith(isLoading: true, error: null);

    try {
      final updatedUser = await _authService.updateProfile(
        firstName: firstName,
        lastName: lastName,
        phone: phone,
      );

      if (updatedUser != null) {
        state = state.copyWith(isLoading: false);
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: 'فشل في تحديث الملف الشخصي',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'خطأ في تحديث الملف الشخصي: ${e.toString()}',
      );
      return false;
    }
  }

  // Change password
  Future<bool> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      // في التطبيق الحقيقي، ستتم مقارنة كلمة المرور الحالية
      state = state.copyWith(isLoading: false);
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'خطأ في تغيير كلمة المرور: ${e.toString()}',
      );
      return false;
    }
  }

  // Forgot password
  Future<bool> forgotPassword(String email) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      await _authService.sendPasswordResetEmail(email);
      state = state.copyWith(isLoading: false);
      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return false;
    }
  }

  // Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Provider
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  return AuthNotifier();
});

// Helper providers
final isLoggedInProvider = Provider<bool>((ref) {
  return ref.watch(authProvider).isLoggedIn;
});

final currentUserProvider = Provider<User?>((ref) {
  return ref.watch(authProvider).user;
});

final authLoadingProvider = Provider<bool>((ref) {
  return ref.watch(authProvider).isLoading;
});

final authErrorProvider = Provider<String?>((ref) {
  return ref.watch(authProvider).error;
});
