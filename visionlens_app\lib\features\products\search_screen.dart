import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';

import '../../shared/widgets/product_card.dart';

class SearchScreen extends ConsumerStatefulWidget {
  const SearchScreen({super.key});

  @override
  ConsumerState<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends ConsumerState<SearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();

  List<Map<String, dynamic>> _searchResults = [];
  List<String> _recentSearches = [];
  List<String> _popularSearches = [];
  bool _isSearching = false;
  bool _showFilters = false;

  // Filter options
  String _selectedCategory = 'الكل';
  String _selectedBrand = 'الكل';
  RangeValues _priceRange = const RangeValues(0, 100000);
  double _minRating = 0;
  bool _inStockOnly = false;

  @override
  void initState() {
    super.initState();
    _loadData();
    _searchFocusNode.requestFocus();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  void _loadData() {
    // TODO: Load from storage/provider
    _recentSearches = ['عدسات يومية', 'عدسات ملونة', 'أكيوفيو'];
    _popularSearches = [
      'عدسات يومية',
      'عدسات شهرية',
      'عدسات ملونة',
      'بايوفينيتي',
      'فريش لوك',
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: _buildSearchAppBar(),
      body: Column(
        children: [
          // Search Bar
          _buildSearchBar(),

          // Filters (if shown)
          if (_showFilters) _buildFiltersSection(),

          // Search Content
          Expanded(
            child: _searchController.text.isEmpty
                ? _buildSearchSuggestions()
                : _isSearching
                ? _buildSearchLoading()
                : _buildSearchResults(),
          ),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildSearchAppBar() {
    return AppBar(
      backgroundColor: AppColors.surface,
      elevation: 0,
      leading: IconButton(
        onPressed: () => Navigator.of(context).pop(),
        icon: const Icon(Icons.arrow_back),
      ),
      title: const Text('البحث'),
      actions: [
        IconButton(
          onPressed: () {
            setState(() {
              _showFilters = !_showFilters;
            });
          },
          icon: Icon(
            Icons.tune,
            color: _showFilters ? AppColors.primary : AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: _searchController,
        focusNode: _searchFocusNode,
        decoration: InputDecoration(
          hintText: 'ابحث عن العدسات...',
          prefixIcon: const Icon(Icons.search, color: AppColors.textSecondary),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  onPressed: () {
                    _searchController.clear();
                    setState(() {
                      _searchResults.clear();
                    });
                  },
                  icon: const Icon(Icons.clear, color: AppColors.textSecondary),
                )
              : null,
          filled: true,
          fillColor: AppColors.surface,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
        ),
        onChanged: _onSearchChanged,
        onSubmitted: _onSearchSubmitted,
      ),
    );
  }

  Widget _buildFiltersSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الفلاتر',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 16),

          // Category Filter
          _buildFilterDropdown(
            'الفئة',
            _selectedCategory,
            ['الكل', 'يومية', 'أسبوعية', 'شهرية', 'ملونة', 'طبية'],
            (value) => setState(() => _selectedCategory = value!),
          ),

          const SizedBox(height: 12),

          // Brand Filter
          _buildFilterDropdown(
            'العلامة التجارية',
            _selectedBrand,
            ['الكل', 'أكيوفيو', 'بايوفينيتي', 'فريش لوك', 'كوبر فيجن'],
            (value) => setState(() => _selectedBrand = value!),
          ),

          const SizedBox(height: 12),

          // Price Range
          Text(
            'نطاق السعر: ${_priceRange.start.round()} - ${_priceRange.end.round()} د.ع',
            style: AppTextStyles.bodyMedium,
          ),
          RangeSlider(
            values: _priceRange,
            min: 0,
            max: 100000,
            divisions: 20,
            onChanged: (values) => setState(() => _priceRange = values),
          ),

          const SizedBox(height: 12),

          // Rating Filter
          Text(
            'التقييم: ${_minRating.round()} نجوم فأكثر',
            style: AppTextStyles.bodyMedium,
          ),
          Slider(
            value: _minRating,
            min: 0,
            max: 5,
            divisions: 5,
            onChanged: (value) => setState(() => _minRating = value),
          ),

          const SizedBox(height: 12),

          // In Stock Only
          CheckboxListTile(
            title: const Text('المتوفر فقط'),
            value: _inStockOnly,
            onChanged: (value) => setState(() => _inStockOnly = value!),
            controlAffinity: ListTileControlAffinity.leading,
            contentPadding: EdgeInsets.zero,
          ),

          const SizedBox(height: 16),

          // Apply Filters Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _applyFilters,
              child: const Text('تطبيق الفلاتر'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterDropdown(
    String label,
    String value,
    List<String> options,
    Function(String?) onChanged,
  ) {
    return Row(
      children: [
        Text(
          '$label: ',
          style: AppTextStyles.bodyMedium.copyWith(fontWeight: FontWeight.w600),
        ),
        Expanded(
          child: DropdownButton<String>(
            value: value,
            isExpanded: true,
            underline: const SizedBox(),
            items: options.map((option) {
              return DropdownMenuItem(value: option, child: Text(option));
            }).toList(),
            onChanged: onChanged,
          ),
        ),
      ],
    );
  }

  Widget _buildSearchSuggestions() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Recent Searches
          if (_recentSearches.isNotEmpty) ...[
            _buildSuggestionSection(
              'البحث الأخير',
              _recentSearches,
              Icons.history,
              true,
            ),
            const SizedBox(height: 24),
          ],

          // Popular Searches
          _buildSuggestionSection(
            'البحث الشائع',
            _popularSearches,
            Icons.trending_up,
            false,
          ),
        ],
      ),
    );
  }

  Widget _buildSuggestionSection(
    String title,
    List<String> suggestions,
    IconData icon,
    bool showClear,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: AppColors.textSecondary, size: 20),
            const SizedBox(width: 8),
            Text(
              title,
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            if (showClear) ...[
              const Spacer(),
              TextButton(
                onPressed: _clearRecentSearches,
                child: const Text('مسح الكل'),
              ),
            ],
          ],
        ),

        const SizedBox(height: 12),

        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: suggestions.map((suggestion) {
            return GestureDetector(
              onTap: () => _selectSuggestion(suggestion),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: AppColors.surface,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: AppColors.border),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(suggestion, style: AppTextStyles.bodyMedium),
                    if (showClear) ...[
                      const SizedBox(width: 8),
                      GestureDetector(
                        onTap: () => _removeRecentSearch(suggestion),
                        child: const Icon(
                          Icons.close,
                          size: 16,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildSearchLoading() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('جاري البحث...'),
        ],
      ),
    );
  }

  Widget _buildSearchResults() {
    if (_searchResults.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 80,
              color: AppColors.textSecondary.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'لم يتم العثور على نتائج',
              style: AppTextStyles.titleMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'جرب كلمات بحث أخرى أو قم بتعديل الفلاتر',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // Results Count
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Text(
                'تم العثور على ${_searchResults.length} منتج',
                style: AppTextStyles.titleSmall.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              DropdownButton<String>(
                value: 'الأحدث',
                underline: const SizedBox(),
                items: const [
                  DropdownMenuItem(value: 'الأحدث', child: Text('الأحدث')),
                  DropdownMenuItem(
                    value: 'السعر: من الأقل للأعلى',
                    child: Text('السعر: من الأقل للأعلى'),
                  ),
                  DropdownMenuItem(
                    value: 'السعر: من الأعلى للأقل',
                    child: Text('السعر: من الأعلى للأقل'),
                  ),
                  DropdownMenuItem(value: 'التقييم', child: Text('التقييم')),
                ],
                onChanged: (value) {
                  // TODO: Implement sorting
                },
              ),
            ],
          ),
        ),

        // Results Grid
        Expanded(
          child: GridView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 0.7,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
            ),
            itemCount: _searchResults.length,
            itemBuilder: (context, index) {
              final product = _searchResults[index];
              return ProductCard(
                imageUrl: product['image'],
                name: product['name'],
                price: product['price'],
                originalPrice: product['originalPrice'],
                rating: product['rating'],
                isInStock: product['isInStock'],
                width: double.infinity,
                onTap: () {
                  // TODO: Navigate to product details
                },
                onAddToCart: () {
                  // TODO: Add to cart
                },
                onToggleFavorite: () {
                  // TODO: Toggle favorite
                },
              );
            },
          ),
        ),
      ],
    );
  }

  void _onSearchChanged(String query) {
    if (query.isEmpty) {
      setState(() {
        _searchResults.clear();
        _isSearching = false;
      });
      return;
    }

    setState(() {
      _isSearching = true;
    });

    // Simulate search delay
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted && _searchController.text == query) {
        _performSearch(query);
      }
    });
  }

  void _onSearchSubmitted(String query) {
    if (query.isNotEmpty) {
      _addToRecentSearches(query);
      _performSearch(query);
    }
  }

  void _performSearch(String query) {
    // TODO: Implement actual search logic
    final dummyResults = _getDummySearchResults(query);

    setState(() {
      _searchResults = dummyResults;
      _isSearching = false;
    });
  }

  void _selectSuggestion(String suggestion) {
    _searchController.text = suggestion;
    _onSearchSubmitted(suggestion);
  }

  void _addToRecentSearches(String query) {
    setState(() {
      _recentSearches.remove(query);
      _recentSearches.insert(0, query);
      if (_recentSearches.length > 5) {
        _recentSearches = _recentSearches.take(5).toList();
      }
    });
    // TODO: Save to storage
  }

  void _removeRecentSearch(String search) {
    setState(() {
      _recentSearches.remove(search);
    });
    // TODO: Update storage
  }

  void _clearRecentSearches() {
    setState(() {
      _recentSearches.clear();
    });
    // TODO: Clear from storage
  }

  void _applyFilters() {
    _performSearch(_searchController.text);
    setState(() {
      _showFilters = false;
    });
  }

  List<Map<String, dynamic>> _getDummySearchResults(String query) {
    // Simulate search results based on query
    final allProducts = [
      {
        'name': 'عدسات أكيوفيو يومية',
        'price': 25000.0,
        'originalPrice': 30000.0,
        'rating': 4.5,
        'image': 'https://via.placeholder.com/200x200',
        'isInStock': true,
      },
      {
        'name': 'عدسات بايوفينيتي شهرية',
        'price': 45000.0,
        'originalPrice': null,
        'rating': 4.8,
        'image': 'https://via.placeholder.com/200x200',
        'isInStock': true,
      },
      {
        'name': 'عدسات فريش لوك ملونة',
        'price': 35000.0,
        'originalPrice': 40000.0,
        'rating': 4.3,
        'image': 'https://via.placeholder.com/200x200',
        'isInStock': false,
      },
    ];

    return allProducts.where((product) {
      final name = product['name'] as String?;
      return name?.toLowerCase().contains(query.toLowerCase()) ?? false;
    }).toList();
  }
}
