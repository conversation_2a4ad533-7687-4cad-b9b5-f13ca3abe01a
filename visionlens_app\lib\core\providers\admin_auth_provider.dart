import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/admin_auth_service.dart';
import '../../shared/models/admin_model.dart';

// Admin Auth State
class AdminAuthState {
  final Admin? admin;
  final bool isLoading;
  final String? error;
  final bool isLoggedIn;

  const AdminAuthState({
    this.admin,
    this.isLoading = false,
    this.error,
    this.isLoggedIn = false,
  });

  AdminAuthState copyWith({
    Admin? admin,
    bool? isLoading,
    String? error,
    bool? isLoggedIn,
  }) {
    return AdminAuthState(
      admin: admin ?? this.admin,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      isLoggedIn: isLoggedIn ?? this.isLoggedIn,
    );
  }
}

// Admin Auth Provider
class AdminAuthNotifier extends StateNotifier<AdminAuthState> {
  final AdminAuthService _authService;

  AdminAuthNotifier(this._authService) : super(const AdminAuthState()) {
    _checkAuthStatus();
  }

  // Check if admin is already logged in
  Future<void> _checkAuthStatus() async {
    state = state.copyWith(isLoading: true);
    
    try {
      final isLoggedIn = await _authService.isLoggedIn();
      if (isLoggedIn) {
        final admin = await _authService.getCurrentAdmin();
        state = state.copyWith(
          admin: admin,
          isLoggedIn: true,
          isLoading: false,
        );
      } else {
        state = state.copyWith(isLoading: false);
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'خطأ في التحقق من حالة تسجيل الدخول',
      );
    }
  }

  // Admin login
  Future<bool> login(String email, String password) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final admin = await _authService.login(email, password);
      
      if (admin != null) {
        state = state.copyWith(
          admin: admin,
          isLoggedIn: true,
          isLoading: false,
        );
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: 'بيانات تسجيل الدخول غير صحيحة',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'خطأ في تسجيل الدخول: ${e.toString()}',
      );
      return false;
    }
  }

  // Admin logout
  Future<bool> logout() async {
    state = state.copyWith(isLoading: true);

    try {
      final success = await _authService.logout();
      
      if (success) {
        state = const AdminAuthState();
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: 'خطأ في تسجيل الخروج',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'خطأ في تسجيل الخروج: ${e.toString()}',
      );
      return false;
    }
  }

  // Update admin profile
  Future<bool> updateProfile({
    String? name,
    String? phoneNumber,
    String? avatar,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final updatedAdmin = await _authService.updateProfile(
        name: name,
        phoneNumber: phoneNumber,
        avatar: avatar,
      );

      if (updatedAdmin != null) {
        state = state.copyWith(
          admin: updatedAdmin,
          isLoading: false,
        );
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: 'فشل في تحديث الملف الشخصي',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'خطأ في تحديث الملف الشخصي: ${e.toString()}',
      );
      return false;
    }
  }

  // Change password
  Future<bool> changePassword(String currentPassword, String newPassword) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final success = await _authService.changePassword(currentPassword, newPassword);
      
      if (success) {
        state = state.copyWith(isLoading: false);
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: 'كلمة المرور الحالية غير صحيحة',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'خطأ في تغيير كلمة المرور: ${e.toString()}',
      );
      return false;
    }
  }

  // Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }

  // Refresh admin data
  Future<void> refreshAdminData() async {
    try {
      final admin = await _authService.getCurrentAdmin();
      if (admin != null) {
        state = state.copyWith(admin: admin);
      }
    } catch (e) {
      // Handle error silently
    }
  }
}

// Provider instances
final adminAuthServiceProvider = Provider<AdminAuthService>((ref) {
  return AdminAuthService();
});

final adminAuthProvider = StateNotifierProvider<AdminAuthNotifier, AdminAuthState>((ref) {
  final authService = ref.watch(adminAuthServiceProvider);
  return AdminAuthNotifier(authService);
});

// Helper providers
final currentAdminProvider = Provider<Admin?>((ref) {
  return ref.watch(adminAuthProvider).admin;
});

final isAdminLoggedInProvider = Provider<bool>((ref) {
  return ref.watch(adminAuthProvider).isLoggedIn;
});
