import 'package:json_annotation/json_annotation.dart';
import 'product_model.dart';

part 'cart_item_model.g.dart';

@JsonSerializable()
class CartItem {
  final String id;
  final Product product;
  final int quantity;
  final DateTime addedAt;
  final Map<String, dynamic>? selectedOptions;

  const CartItem({
    required this.id,
    required this.product,
    required this.quantity,
    required this.addedAt,
    this.selectedOptions,
  });

  // Calculated properties
  double get unitPrice => product.finalPrice;
  double get totalPrice => unitPrice * quantity;
  double get originalTotalPrice => product.price * quantity;
  double get discountAmount => originalTotalPrice - totalPrice;
  bool get hasDiscount => product.hasDiscount;

  CartItem copyWith({
    String? id,
    Product? product,
    int? quantity,
    DateTime? addedAt,
    Map<String, dynamic>? selectedOptions,
  }) {
    return CartItem(
      id: id ?? this.id,
      product: product ?? this.product,
      quantity: quantity ?? this.quantity,
      addedAt: addedAt ?? this.addedAt,
      selectedOptions: selectedOptions ?? this.selectedOptions,
    );
  }

  factory CartItem.fromJson(Map<String, dynamic> json) =>
      _$CartItemFromJson(json);
  Map<String, dynamic> toJson() => _$CartItemToJson(this);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CartItem && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'CartItem{id: $id, product: ${product.name}, quantity: $quantity, totalPrice: $totalPrice}';
  }
}
