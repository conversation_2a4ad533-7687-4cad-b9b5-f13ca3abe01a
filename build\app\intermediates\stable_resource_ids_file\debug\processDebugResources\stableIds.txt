com.example.visionlensapp:styleable/SplitPairRule = 0x7f0d000b
com.example.visionlensapp:styleable/GradientColorItem = 0x7f0d0009
com.example.visionlensapp:styleable/GradientColor = 0x7f0d0008
com.example.visionlensapp:styleable/Fragment = 0x7f0d0006
com.example.visionlensapp:styleable/FontFamilyFont = 0x7f0d0005
com.example.visionlensapp:styleable/ActivityRule = 0x7f0d0001
com.example.visionlensapp:styleable/ActivityFilter = 0x7f0d0000
com.example.visionlensapp:style/Widget.Compat.NotificationActionText = 0x7f0c0008
com.example.visionlensapp:style/Widget.Compat.NotificationActionContainer = 0x7f0c0007
com.example.visionlensapp:style/TextAppearance.Compat.Notification.Time = 0x7f0c0005
com.example.visionlensapp:style/TextAppearance.Compat.Notification.Line2 = 0x7f0c0004
com.example.visionlensapp:style/TextAppearance.Compat.Notification.Info = 0x7f0c0003
com.example.visionlensapp:style/TextAppearance.Compat.Notification = 0x7f0c0002
com.example.visionlensapp:style/LaunchTheme = 0x7f0c0000
com.example.visionlensapp:string/status_bar_notification_info_overflow = 0x7f0b0008
com.example.visionlensapp:string/call_notification_screening_text = 0x7f0b0007
com.example.visionlensapp:string/call_notification_ongoing_text = 0x7f0b0006
com.example.visionlensapp:string/call_notification_decline_action = 0x7f0b0003
com.example.visionlensapp:string/call_notification_answer_video_action = 0x7f0b0002
com.example.visionlensapp:string/androidx_startup = 0x7f0b0000
com.example.visionlensapp:layout/notification_template_part_time = 0x7f090008
com.example.visionlensapp:id/androidx_window_activity_scope = 0x7f07002a
com.example.visionlensapp:layout/notification_template_custom_big = 0x7f090005
com.example.visionlensapp:layout/notification_action_tombstone = 0x7f090004
com.example.visionlensapp:layout/ime_base_split_test_activity = 0x7f090001
com.example.visionlensapp:id/view_tree_view_model_store_owner = 0x7f07005b
com.example.visionlensapp:attr/stickyPlaceholder = 0x7f030023
com.example.visionlensapp:id/action_image = 0x7f070023
com.example.visionlensapp:id/info = 0x7f070036
com.example.visionlensapp:id/view_tree_on_back_pressed_dispatcher_owner = 0x7f070059
com.example.visionlensapp:id/visible_removing_fragment_view_tag = 0x7f07005c
com.example.visionlensapp:id/view_tree_lifecycle_owner = 0x7f070058
com.example.visionlensapp:style/TextAppearance.Compat.Notification.Title = 0x7f0c0006
com.example.visionlensapp:id/topToBottom = 0x7f070057
com.example.visionlensapp:id/tag_transition_group = 0x7f07004f
com.example.visionlensapp:id/tag_on_receive_content_mime_types = 0x7f07004c
com.example.visionlensapp:id/tag_on_apply_window_listener = 0x7f07004a
com.example.visionlensapp:layout/notification_template_part_chronometer = 0x7f090007
com.example.visionlensapp:styleable/SplitPlaceholderRule = 0x7f0d000c
com.example.visionlensapp:drawable/ic_call_answer = 0x7f060000
com.example.visionlensapp:id/tag_accessibility_pane_title = 0x7f070049
com.example.visionlensapp:id/accessibility_custom_action_2 = 0x7f07000d
com.example.visionlensapp:id/tag_accessibility_actions = 0x7f070046
com.example.visionlensapp:layout/notification_template_icon_group = 0x7f090006
com.example.visionlensapp:id/rtl = 0x7f070044
com.example.visionlensapp:id/notification_main_column_container = 0x7f070040
com.example.visionlensapp:id/normal = 0x7f07003d
com.example.visionlensapp:color/notification_action_color_filter = 0x7f040004
com.example.visionlensapp:attr/fontProviderCerts = 0x7f03000b
com.example.visionlensapp:id/italic = 0x7f070037
com.example.visionlensapp:id/title = 0x7f070056
com.example.visionlensapp:attr/animationBackgroundColor = 0x7f030004
com.example.visionlensapp:id/icon_group = 0x7f070035
com.example.visionlensapp:id/hide_ime_id = 0x7f070033
com.example.visionlensapp:id/fragment_container_view_tag = 0x7f070032
com.example.visionlensapp:id/dialog_button = 0x7f07002f
com.example.visionlensapp:id/alwaysDisallow = 0x7f070029
com.example.visionlensapp:id/edit_text_id = 0x7f070030
com.example.visionlensapp:id/always = 0x7f070027
com.example.visionlensapp:dimen/notification_large_icon_width = 0x7f05000c
com.example.visionlensapp:id/adjacent = 0x7f070026
com.example.visionlensapp:id/never = 0x7f07003c
com.example.visionlensapp:id/text = 0x7f070053
com.example.visionlensapp:id/accessibility_custom_action_3 = 0x7f070018
com.example.visionlensapp:id/action_divider = 0x7f070022
com.example.visionlensapp:dimen/notification_large_icon_height = 0x7f05000b
com.example.visionlensapp:id/forever = 0x7f070031
com.example.visionlensapp:id/accessibility_custom_action_9 = 0x7f070020
com.example.visionlensapp:id/right_side = 0x7f070043
com.example.visionlensapp:id/accessibility_custom_action_8 = 0x7f07001f
com.example.visionlensapp:id/report_drawn = 0x7f070041
com.example.visionlensapp:attr/clearTop = 0x7f030005
com.example.visionlensapp:dimen/compat_button_padding_horizontal_material = 0x7f050002
com.example.visionlensapp:id/accessibility_custom_action_6 = 0x7f07001d
com.example.visionlensapp:id/accessibility_custom_action_5 = 0x7f07001c
com.example.visionlensapp:id/accessibility_custom_action_4 = 0x7f07001b
com.example.visionlensapp:id/tag_window_insets_animation_callback = 0x7f070052
com.example.visionlensapp:id/accessibility_custom_action_31 = 0x7f07001a
com.example.visionlensapp:id/accessibility_custom_action_29 = 0x7f070017
com.example.visionlensapp:integer/status_bar_notification_info_maxnum = 0x7f080000
com.example.visionlensapp:id/accessibility_custom_action_28 = 0x7f070016
com.example.visionlensapp:dimen/compat_control_corner_material = 0x7f050004
com.example.visionlensapp:id/accessibility_custom_action_27 = 0x7f070015
com.example.visionlensapp:id/tag_unhandled_key_event_manager = 0x7f070050
com.example.visionlensapp:id/blocking = 0x7f07002c
com.example.visionlensapp:id/accessibility_custom_action_26 = 0x7f070014
com.example.visionlensapp:attr/fontStyle = 0x7f030011
com.example.visionlensapp:id/line3 = 0x7f070039
com.example.visionlensapp:attr/shortcutMatchRequired = 0x7f03001b
com.example.visionlensapp:id/accessibility_custom_action_10 = 0x7f070003
com.example.visionlensapp:id/accessibility_custom_action_25 = 0x7f070013
com.example.visionlensapp:id/action_container = 0x7f070021
com.example.visionlensapp:attr/primaryActivityName = 0x7f030017
com.example.visionlensapp:id/accessibility_custom_action_24 = 0x7f070012
com.example.visionlensapp:id/accessibility_custom_action_23 = 0x7f070011
com.example.visionlensapp:dimen/notification_small_icon_background_padding = 0x7f050011
com.example.visionlensapp:id/accessibility_custom_action_20 = 0x7f07000e
com.example.visionlensapp:id/accessibility_custom_action_7 = 0x7f07001e
com.example.visionlensapp:id/accessibility_action_clickable_span = 0x7f070000
com.example.visionlensapp:id/accessibility_custom_action_18 = 0x7f07000b
com.example.visionlensapp:id/accessibility_custom_action_16 = 0x7f070009
com.example.visionlensapp:id/icon = 0x7f070034
com.example.visionlensapp:attr/splitMinSmallestWidthDp = 0x7f030020
com.example.visionlensapp:id/accessibility_custom_action_30 = 0x7f070019
com.example.visionlensapp:id/accessibility_custom_action_15 = 0x7f070008
com.example.visionlensapp:id/accessibility_custom_action_13 = 0x7f070006
com.example.visionlensapp:id/accessibility_custom_action_12 = 0x7f070005
com.example.visionlensapp:dimen/notification_right_icon_size = 0x7f05000f
com.example.visionlensapp:drawable/ic_call_decline_low = 0x7f060005
com.example.visionlensapp:drawable/notification_icon_background = 0x7f06000e
com.example.visionlensapp:drawable/notify_panel_notification_icon_bg = 0x7f060013
com.example.visionlensapp:attr/secondaryActivityAction = 0x7f030019
com.example.visionlensapp:drawable/notification_bg_normal = 0x7f06000c
com.example.visionlensapp:drawable/notification_tile_bg = 0x7f060012
com.example.visionlensapp:id/special_effects_controller_view_tag = 0x7f070045
com.example.visionlensapp:drawable/notification_oversize_large_icon_bg = 0x7f06000f
com.example.visionlensapp:drawable/notification_bg_low_pressed = 0x7f06000b
com.example.visionlensapp:drawable/launch_background = 0x7f060006
com.example.visionlensapp:id/locale = 0x7f07003a
com.example.visionlensapp:string/call_notification_incoming_text = 0x7f0b0005
com.example.visionlensapp:id/chronometer = 0x7f07002e
com.example.visionlensapp:dimen/notification_action_icon_size = 0x7f050007
com.example.visionlensapp:styleable/SplitPairFilter = 0x7f0d000a
com.example.visionlensapp:drawable/ic_call_decline = 0x7f060004
com.example.visionlensapp:drawable/ic_call_answer_video_low = 0x7f060003
com.example.visionlensapp:id/notification_main_column = 0x7f07003f
com.example.visionlensapp:attr/secondaryActivityName = 0x7f03001a
com.example.visionlensapp:attr/finishSecondaryWithPrimary = 0x7f030008
com.example.visionlensapp:dimen/notification_top_pad = 0x7f050014
com.example.visionlensapp:attr/splitMaxAspectRatioInLandscape = 0x7f03001d
com.example.visionlensapp:id/notification_background = 0x7f07003e
com.example.visionlensapp:string/call_notification_hang_up_action = 0x7f0b0004
com.example.visionlensapp:id/ltr = 0x7f07003b
com.example.visionlensapp:id/accessibility_custom_action_21 = 0x7f07000f
com.example.visionlensapp:id/tag_on_receive_content_listener = 0x7f07004b
com.example.visionlensapp:attr/fontVariationSettings = 0x7f030012
com.example.visionlensapp:dimen/notification_right_side_padding_top = 0x7f050010
com.example.visionlensapp:dimen/notification_media_narrow_margin = 0x7f05000e
com.example.visionlensapp:dimen/notification_content_margin_start = 0x7f05000a
com.example.visionlensapp:drawable/notification_bg_normal_pressed = 0x7f06000d
com.example.visionlensapp:layout/ime_secondary_split_test_activity = 0x7f090002
com.example.visionlensapp:attr/splitMinWidthDp = 0x7f030021
com.example.visionlensapp:id/accessibility_custom_action_11 = 0x7f070004
com.example.visionlensapp:attr/fontWeight = 0x7f030013
com.example.visionlensapp:dimen/notification_top_pad_large_text = 0x7f050015
com.example.visionlensapp:attr/activityName = 0x7f030001
com.example.visionlensapp:animator/fragment_open_exit = 0x7f020005
com.example.visionlensapp:dimen/notification_big_circle_margin = 0x7f050009
com.example.visionlensapp:color/androidx_core_secondary_text_default_material_light = 0x7f040001
com.example.visionlensapp:dimen/notification_action_text_size = 0x7f050008
com.example.visionlensapp:anim/fragment_fast_out_extra_slow_in = 0x7f010000
com.example.visionlensapp:dimen/compat_button_inset_vertical_material = 0x7f050001
com.example.visionlensapp:dimen/compat_button_inset_horizontal_material = 0x7f050000
com.example.visionlensapp:attr/finishPrimaryWithSecondary = 0x7f030007
com.example.visionlensapp:color/notification_icon_bg_color = 0x7f040005
com.example.visionlensapp:id/accessibility_custom_action_0 = 0x7f070001
com.example.visionlensapp:id/right_icon = 0x7f070042
com.example.visionlensapp:id/tag_unhandled_key_listeners = 0x7f070051
com.example.visionlensapp:id/tag_state_description = 0x7f07004e
com.example.visionlensapp:color/call_notification_decline_color = 0x7f040003
com.example.visionlensapp:attr/splitMinHeightDp = 0x7f03001f
com.example.visionlensapp:id/accessibility_custom_action_22 = 0x7f070010
com.example.visionlensapp:string/call_notification_answer_action = 0x7f0b0001
com.example.visionlensapp:animator/fragment_close_exit = 0x7f020001
com.example.visionlensapp:drawable/notification_bg_low_normal = 0x7f06000a
com.example.visionlensapp:id/tag_accessibility_clickable_spans = 0x7f070047
com.example.visionlensapp:attr/splitLayoutDirection = 0x7f03001c
com.example.visionlensapp:id/accessibility_custom_action_1 = 0x7f070002
com.example.visionlensapp:attr/ttcIndex = 0x7f030025
com.example.visionlensapp:attr/tag = 0x7f030024
com.example.visionlensapp:id/tag_screen_reader_focusable = 0x7f07004d
com.example.visionlensapp:id/alwaysAllow = 0x7f070028
com.example.visionlensapp:drawable/notification_template_icon_low_bg = 0x7f060011
com.example.visionlensapp:drawable/notification_action_background = 0x7f060007
com.example.visionlensapp:attr/fontProviderPackage = 0x7f03000e
com.example.visionlensapp:dimen/notification_small_icon_size_as_large = 0x7f050012
com.example.visionlensapp:styleable/ColorStateListItem = 0x7f0d0003
com.example.visionlensapp:id/tag_accessibility_heading = 0x7f070048
com.example.visionlensapp:drawable/notification_bg = 0x7f060008
com.example.visionlensapp:animator/fragment_fade_exit = 0x7f020003
com.example.visionlensapp:layout/custom_dialog = 0x7f090000
com.example.visionlensapp:animator/fragment_close_enter = 0x7f020000
com.example.visionlensapp:id/time = 0x7f070055
com.example.visionlensapp:id/action_text = 0x7f070024
com.example.visionlensapp:styleable/FragmentContainerView = 0x7f0d0007
com.example.visionlensapp:animator/fragment_open_enter = 0x7f020004
com.example.visionlensapp:styleable/Capability = 0x7f0d0002
com.example.visionlensapp:attr/nestedScrollViewStyle = 0x7f030015
com.example.visionlensapp:attr/placeholderActivityName = 0x7f030016
com.example.visionlensapp:id/async = 0x7f07002b
com.example.visionlensapp:attr/splitRatio = 0x7f030022
com.example.visionlensapp:attr/fontProviderSystemFontFamily = 0x7f030010
com.example.visionlensapp:attr/fontProviderAuthority = 0x7f03000a
com.example.visionlensapp:attr/fontProviderQuery = 0x7f03000f
com.example.visionlensapp:styleable/FontFamily = 0x7f0d0004
com.example.visionlensapp:dimen/compat_notification_large_icon_max_width = 0x7f050006
com.example.visionlensapp:attr/alpha = 0x7f030002
com.example.visionlensapp:id/accessibility_custom_action_19 = 0x7f07000c
com.example.visionlensapp:mipmap/ic_launcher = 0x7f0a0000
com.example.visionlensapp:attr/fontProviderFetchTimeout = 0x7f03000d
com.example.visionlensapp:drawable/notification_bg_low = 0x7f060009
com.example.visionlensapp:id/accessibility_custom_action_17 = 0x7f07000a
com.example.visionlensapp:style/NormalTheme = 0x7f0c0001
com.example.visionlensapp:drawable/ic_call_answer_low = 0x7f060001
com.example.visionlensapp:attr/finishPrimaryWithPlaceholder = 0x7f030006
com.example.visionlensapp:attr/font = 0x7f030009
com.example.visionlensapp:attr/queryPatterns = 0x7f030018
com.example.visionlensapp:dimen/notification_main_column_padding_top = 0x7f05000d
com.example.visionlensapp:attr/fontProviderFetchStrategy = 0x7f03000c
com.example.visionlensapp:dimen/compat_button_padding_vertical_material = 0x7f050003
com.example.visionlensapp:id/actions = 0x7f070025
com.example.visionlensapp:drawable/notification_template_icon_bg = 0x7f060010
com.example.visionlensapp:attr/splitMaxAspectRatioInPortrait = 0x7f03001e
com.example.visionlensapp:id/bottomToTop = 0x7f07002d
com.example.visionlensapp:attr/alwaysExpand = 0x7f030003
com.example.visionlensapp:color/call_notification_answer_color = 0x7f040002
com.example.visionlensapp:layout/notification_action = 0x7f090003
com.example.visionlensapp:id/text2 = 0x7f070054
com.example.visionlensapp:color/androidx_core_ripple_material_light = 0x7f040000
com.example.visionlensapp:attr/lStar = 0x7f030014
com.example.visionlensapp:dimen/compat_notification_large_icon_max_height = 0x7f050005
com.example.visionlensapp:attr/activityAction = 0x7f030000
com.example.visionlensapp:animator/fragment_fade_enter = 0x7f020002
com.example.visionlensapp:id/accessibility_custom_action_14 = 0x7f070007
com.example.visionlensapp:dimen/notification_subtext_size = 0x7f050013
com.example.visionlensapp:id/line1 = 0x7f070038
com.example.visionlensapp:drawable/ic_call_answer_video = 0x7f060002
com.example.visionlensapp:id/view_tree_saved_state_registry_owner = 0x7f07005a
