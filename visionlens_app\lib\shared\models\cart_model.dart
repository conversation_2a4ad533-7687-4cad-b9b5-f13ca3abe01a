import 'package:json_annotation/json_annotation.dart';
import 'product_model.dart';

part 'cart_model.g.dart';

@JsonSerializable()
class Cart {
  final int id;
  final int userId;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @<PERSON><PERSON><PERSON>ey(name: 'updated_at')
  final DateTime updatedAt;
  final List<CartItem> items;

  const Cart({
    required this.id,
    required this.userId,
    required this.createdAt,
    required this.updatedAt,
    required this.items,
  });

  factory Cart.fromJson(Map<String, dynamic> json) => _$CartFromJson(json);
  Map<String, dynamic> toJson() => _$CartToJson(this);

  double get subtotal {
    return items.fold(0.0, (sum, item) => sum + item.total);
  }

  int get totalItems {
    return items.fold(0, (sum, item) => sum + item.quantity);
  }

  bool get isEmpty => items.isEmpty;
  bool get isNotEmpty => items.isNotEmpty;

  Cart copyWith({
    int? id,
    int? userId,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<CartItem>? items,
  }) {
    return Cart(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      items: items ?? this.items,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Cart && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Cart(id: $id, items: ${items.length}, subtotal: $subtotal)';
  }
}

@JsonSerializable()
class CartItem {
  final int id;
  final int cartId;
  final Product product;
  final int quantity;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;

  const CartItem({
    required this.id,
    required this.cartId,
    required this.product,
    required this.quantity,
    required this.createdAt,
  });

  factory CartItem.fromJson(Map<String, dynamic> json) => _$CartItemFromJson(json);
  Map<String, dynamic> toJson() => _$CartItemToJson(this);

  double get total => product.finalPrice * quantity;
  double get savings => product.hasDiscount ? (product.price - product.finalPrice) * quantity : 0;

  CartItem copyWith({
    int? id,
    int? cartId,
    Product? product,
    int? quantity,
    DateTime? createdAt,
  }) {
    return CartItem(
      id: id ?? this.id,
      cartId: cartId ?? this.cartId,
      product: product ?? this.product,
      quantity: quantity ?? this.quantity,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CartItem && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'CartItem(id: $id, product: ${product.name}, quantity: $quantity, total: $total)';
  }
}

@JsonSerializable()
class AddToCartRequest {
  @JsonKey(name: 'product_id')
  final int productId;
  final int quantity;

  const AddToCartRequest({
    required this.productId,
    required this.quantity,
  });

  factory AddToCartRequest.fromJson(Map<String, dynamic> json) => _$AddToCartRequestFromJson(json);
  Map<String, dynamic> toJson() => _$AddToCartRequestToJson(this);
}

@JsonSerializable()
class UpdateCartItemRequest {
  final int quantity;

  const UpdateCartItemRequest({
    required this.quantity,
  });

  factory UpdateCartItemRequest.fromJson(Map<String, dynamic> json) => _$UpdateCartItemRequestFromJson(json);
  Map<String, dynamic> toJson() => _$UpdateCartItemRequestToJson(this);
}

// Local cart model for offline storage
@JsonSerializable()
class LocalCart {
  final List<LocalCartItem> items;
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  const LocalCart({
    required this.items,
    required this.updatedAt,
  });

  factory LocalCart.fromJson(Map<String, dynamic> json) => _$LocalCartFromJson(json);
  Map<String, dynamic> toJson() => _$LocalCartToJson(this);

  factory LocalCart.empty() {
    return LocalCart(
      items: [],
      updatedAt: DateTime.now(),
    );
  }

  double get subtotal {
    return items.fold(0.0, (sum, item) => sum + item.total);
  }

  int get totalItems {
    return items.fold(0, (sum, item) => sum + item.quantity);
  }

  bool get isEmpty => items.isEmpty;
  bool get isNotEmpty => items.isNotEmpty;

  LocalCart addItem(int productId, String productName, double price, int quantity) {
    final existingIndex = items.indexWhere((item) => item.productId == productId);
    
    if (existingIndex >= 0) {
      final updatedItems = List<LocalCartItem>.from(items);
      updatedItems[existingIndex] = updatedItems[existingIndex].copyWith(
        quantity: updatedItems[existingIndex].quantity + quantity,
      );
      return copyWith(items: updatedItems);
    } else {
      final newItem = LocalCartItem(
        productId: productId,
        productName: productName,
        price: price,
        quantity: quantity,
      );
      return copyWith(items: [...items, newItem]);
    }
  }

  LocalCart updateItem(int productId, int quantity) {
    if (quantity <= 0) {
      return removeItem(productId);
    }

    final updatedItems = items.map((item) {
      if (item.productId == productId) {
        return item.copyWith(quantity: quantity);
      }
      return item;
    }).toList();

    return copyWith(items: updatedItems);
  }

  LocalCart removeItem(int productId) {
    final updatedItems = items.where((item) => item.productId != productId).toList();
    return copyWith(items: updatedItems);
  }

  LocalCart clear() {
    return copyWith(items: []);
  }

  LocalCart copyWith({
    List<LocalCartItem>? items,
    DateTime? updatedAt,
  }) {
    return LocalCart(
      items: items ?? this.items,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }
}

@JsonSerializable()
class LocalCartItem {
  @JsonKey(name: 'product_id')
  final int productId;
  @JsonKey(name: 'product_name')
  final String productName;
  final double price;
  final int quantity;

  const LocalCartItem({
    required this.productId,
    required this.productName,
    required this.price,
    required this.quantity,
  });

  factory LocalCartItem.fromJson(Map<String, dynamic> json) => _$LocalCartItemFromJson(json);
  Map<String, dynamic> toJson() => _$LocalCartItemToJson(this);

  double get total => price * quantity;

  LocalCartItem copyWith({
    int? productId,
    String? productName,
    double? price,
    int? quantity,
  }) {
    return LocalCartItem(
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      price: price ?? this.price,
      quantity: quantity ?? this.quantity,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LocalCartItem && other.productId == productId;
  }

  @override
  int get hashCode => productId.hashCode;

  @override
  String toString() {
    return 'LocalCartItem(productId: $productId, name: $productName, quantity: $quantity, total: $total)';
  }
}
