import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';

class AddressSection extends StatefulWidget {
  final String? selectedAddressId;
  final Function(String) onAddressSelected;
  final Function(Map<String, dynamic>) onNewAddress;

  const AddressSection({
    super.key,
    this.selectedAddressId,
    required this.onAddressSelected,
    required this.onNewAddress,
  });

  @override
  State<AddressSection> createState() => _AddressSectionState();
}

class _AddressSectionState extends State<AddressSection> {
  List<Map<String, dynamic>> _savedAddresses = [];
  bool _showAddNewAddress = false;

  // Form controllers
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _cityController = TextEditingController();
  final _areaController = TextEditingController();
  final _streetController = TextEditingController();
  final _buildingController = TextEditingController();
  final _notesController = TextEditingController();

  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _loadSavedAddresses();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _cityController.dispose();
    _areaController.dispose();
    _streetController.dispose();
    _buildingController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  void _loadSavedAddresses() {
    // TODO: Load from storage/API
    setState(() {
      _savedAddresses = [
        {
          'id': '1',
          'name': 'المنزل',
          'fullName': 'أحمد محمد',
          'phone': '+964 ************',
          'city': 'بغداد',
          'area': 'الكرادة',
          'street': 'شارع الكرادة الداخلية',
          'building': 'بناية رقم 15، الطابق الثالث',
          'isDefault': true,
        },
        {
          'id': '2',
          'name': 'العمل',
          'fullName': 'أحمد محمد',
          'phone': '+964 ************',
          'city': 'بغداد',
          'area': 'المنصور',
          'street': 'شارع الأميرات',
          'building': 'مجمع الأميرات التجاري، الطابق الثاني',
          'isDefault': false,
        },
      ];
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'عنوان التوصيل',
          style: AppTextStyles.headlineSmall.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),

        const SizedBox(height: 20),

        // Saved Addresses
        if (_savedAddresses.isNotEmpty) ...[
          Text(
            'العناوين المحفوظة',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),

          ..._savedAddresses.map((address) => _buildAddressCard(address)),

          const SizedBox(height: 20),
        ],

        // Add New Address Button
        if (!_showAddNewAddress)
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () {
                setState(() {
                  _showAddNewAddress = true;
                });
              },
              icon: const Icon(Icons.add),
              label: const Text('إضافة عنوان جديد'),
            ),
          ),

        // New Address Form
        if (_showAddNewAddress) _buildNewAddressForm(),
      ],
    );
  }

  Widget _buildAddressCard(Map<String, dynamic> address) {
    final isSelected = widget.selectedAddressId == address['id'];

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => widget.onAddressSelected(address['id']),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected ? AppColors.primary : AppColors.border,
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    isSelected
                        ? Icons.radio_button_checked
                        : Icons.radio_button_unchecked,
                    color: isSelected
                        ? AppColors.primary
                        : AppColors.textSecondary,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Row(
                      children: [
                        Text(
                          address['name'],
                          style: AppTextStyles.titleMedium.copyWith(
                            fontWeight: FontWeight.w600,
                            color: isSelected
                                ? AppColors.primary
                                : AppColors.textPrimary,
                          ),
                        ),
                        if (address['isDefault']) ...[
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.primary.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              'افتراضي',
                              style: AppTextStyles.bodySmall.copyWith(
                                color: AppColors.primary,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => _editAddress(address),
                    icon: const Icon(Icons.edit, size: 20),
                    style: IconButton.styleFrom(
                      foregroundColor: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              Text(
                address['fullName'],
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),

              const SizedBox(height: 4),

              Text(
                address['phone'],
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),

              const SizedBox(height: 8),

              Text(
                '${address['city']} - ${address['area']}',
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),

              const SizedBox(height: 4),

              Text(
                '${address['street']}, ${address['building']}',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNewAddressForm() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.border),
      ),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'عنوان جديد',
                  style: AppTextStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    setState(() {
                      _showAddNewAddress = false;
                    });
                  },
                  child: const Text('إلغاء'),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // Full Name
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'الاسم الكامل',
                prefixIcon: Icon(Icons.person),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال الاسم الكامل';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // Phone
            TextFormField(
              controller: _phoneController,
              decoration: const InputDecoration(
                labelText: 'رقم الهاتف',
                prefixIcon: Icon(Icons.phone),
              ),
              keyboardType: TextInputType.phone,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال رقم الهاتف';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // City and Area
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _cityController,
                    decoration: const InputDecoration(
                      labelText: 'المدينة',
                      prefixIcon: Icon(Icons.location_city),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال المدينة';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _areaController,
                    decoration: const InputDecoration(
                      labelText: 'المنطقة',
                      prefixIcon: Icon(Icons.location_on),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال المنطقة';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Street
            TextFormField(
              controller: _streetController,
              decoration: const InputDecoration(
                labelText: 'الشارع',
                prefixIcon: Icon(Icons.location_on),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال اسم الشارع';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // Building
            TextFormField(
              controller: _buildingController,
              decoration: const InputDecoration(
                labelText: 'رقم البناية والطابق',
                prefixIcon: Icon(Icons.business),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال رقم البناية';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // Notes
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'ملاحظات إضافية (اختياري)',
                prefixIcon: Icon(Icons.note),
              ),
              maxLines: 2,
            ),

            const SizedBox(height: 20),

            // Save Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _saveNewAddress,
                child: const Text('حفظ العنوان'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _saveNewAddress() {
    if (_formKey.currentState!.validate()) {
      final newAddress = {
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
        'name': 'عنوان جديد',
        'fullName': _nameController.text,
        'phone': _phoneController.text,
        'city': _cityController.text,
        'area': _areaController.text,
        'street': _streetController.text,
        'building': _buildingController.text,
        'notes': _notesController.text,
        'isDefault': false,
      };

      widget.onNewAddress(newAddress);
      widget.onAddressSelected(newAddress['id'] as String);

      setState(() {
        _savedAddresses.add(newAddress);
        _showAddNewAddress = false;
      });

      // Clear form
      _nameController.clear();
      _phoneController.clear();
      _cityController.clear();
      _areaController.clear();
      _streetController.clear();
      _buildingController.clear();
      _notesController.clear();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم حفظ العنوان بنجاح'),
          backgroundColor: AppColors.success,
        ),
      );
    }
  }

  void _editAddress(Map<String, dynamic> address) {
    // TODO: Implement edit address functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تعديل العنوان قريباً...'),
        backgroundColor: AppColors.info,
      ),
    );
  }
}
