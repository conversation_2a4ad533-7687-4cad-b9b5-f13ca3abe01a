import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';
import '../constants/app_constants.dart';
import '../../shared/models/cart_item_model.dart';
import '../../shared/models/product_model.dart';

// Cart State
class CartState {
  final List<CartItem> items;
  final bool isLoading;
  final String? error;

  const CartState({this.items = const [], this.isLoading = false, this.error});

  CartState copyWith({List<CartItem>? items, bool? isLoading, String? error}) {
    return CartState(
      items: items ?? this.items,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }

  // Calculated properties
  int get totalItems => items.fold(0, (sum, item) => sum + item.quantity);

  double get subtotal => items.fold(0.0, (sum, item) => sum + item.totalPrice);

  double get shipping => subtotal >= AppConstants.freeShippingThreshold
      ? 0.0
      : AppConstants.defaultShippingCost;

  double get tax => 0.0; // No tax for now

  double get total => subtotal + shipping + tax;

  bool get isEmpty => items.isEmpty;

  bool get isNotEmpty => items.isNotEmpty;
}

// Cart Notifier
class CartNotifier extends StateNotifier<CartState> {
  CartNotifier() : super(const CartState()) {
    _loadCartFromStorage();
  }

  // Load cart from local storage
  Future<void> _loadCartFromStorage() async {
    try {
      final box = await Hive.openBox('cart');
      final cartData = box.get(AppConstants.cartKey);

      if (cartData != null) {
        final List<dynamic> itemsList = cartData;
        final items = itemsList
            .map((item) => CartItem.fromJson(Map<String, dynamic>.from(item)))
            .toList();

        state = state.copyWith(items: items);
      }
    } catch (e) {
      print('Error loading cart from storage: $e');
    }
  }

  // Save cart to local storage
  Future<void> _saveCartToStorage() async {
    try {
      final box = await Hive.openBox('cart');
      final cartData = state.items.map((item) => item.toJson()).toList();
      await box.put(AppConstants.cartKey, cartData);
    } catch (e) {
      print('Error saving cart to storage: $e');
    }
  }

  // Add item to cart
  Future<void> addItem(Product product, {int quantity = 1}) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final existingItemIndex = state.items.indexWhere(
        (item) => item.product.id == product.id,
      );

      List<CartItem> updatedItems = List.from(state.items);

      if (existingItemIndex >= 0) {
        // Update existing item quantity
        final existingItem = updatedItems[existingItemIndex];
        updatedItems[existingItemIndex] = existingItem.copyWith(
          quantity: existingItem.quantity + quantity,
        );
      } else {
        // Add new item
        final cartItem = CartItem(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          product: product,
          quantity: quantity,
          addedAt: DateTime.now(),
        );
        updatedItems.add(cartItem);
      }

      state = state.copyWith(items: updatedItems, isLoading: false);

      await _saveCartToStorage();
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'خطأ في إضافة المنتج للسلة: ${e.toString()}',
      );
    }
  }

  // Remove item from cart
  Future<void> removeItem(String itemId) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final updatedItems = state.items
          .where((item) => item.id != itemId)
          .toList();

      state = state.copyWith(items: updatedItems, isLoading: false);

      await _saveCartToStorage();
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'خطأ في حذف المنتج من السلة: ${e.toString()}',
      );
    }
  }

  // Update item quantity
  Future<void> updateQuantity(String itemId, int newQuantity) async {
    if (newQuantity <= 0) {
      await removeItem(itemId);
      return;
    }

    state = state.copyWith(isLoading: true, error: null);

    try {
      final updatedItems = state.items.map((item) {
        if (item.id == itemId) {
          return item.copyWith(quantity: newQuantity);
        }
        return item;
      }).toList();

      state = state.copyWith(items: updatedItems, isLoading: false);

      await _saveCartToStorage();
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'خطأ في تحديث الكمية: ${e.toString()}',
      );
    }
  }

  // Clear cart
  Future<void> clearCart() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      state = state.copyWith(items: [], isLoading: false);

      await _saveCartToStorage();
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'خطأ في مسح السلة: ${e.toString()}',
      );
    }
  }

  // Get item by product id
  CartItem? getItemByProductId(int productId) {
    try {
      return state.items.firstWhere((item) => item.product.id == productId);
    } catch (e) {
      return null;
    }
  }

  // Check if product is in cart
  bool isProductInCart(int productId) {
    return state.items.any((item) => item.product.id == productId);
  }

  // Get product quantity in cart
  int getProductQuantity(int productId) {
    final item = getItemByProductId(productId);
    return item?.quantity ?? 0;
  }

  // Apply coupon (placeholder)
  Future<bool> applyCoupon(String couponCode) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // Simulate API call to validate coupon
      await Future.delayed(const Duration(seconds: 1));

      // Mock coupon validation
      if (couponCode.toLowerCase() == 'welcome10') {
        // Apply 10% discount
        state = state.copyWith(isLoading: false);
        return true;
      } else {
        state = state.copyWith(isLoading: false, error: 'كود الخصم غير صحيح');
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'خطأ في تطبيق كود الخصم: ${e.toString()}',
      );
      return false;
    }
  }

  // Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Provider
final cartProvider = StateNotifierProvider<CartNotifier, CartState>((ref) {
  return CartNotifier();
});

// Helper providers
final cartItemsProvider = Provider<List<CartItem>>((ref) {
  return ref.watch(cartProvider).items;
});

final cartTotalItemsProvider = Provider<int>((ref) {
  return ref.watch(cartProvider).totalItems;
});

final cartSubtotalProvider = Provider<double>((ref) {
  return ref.watch(cartProvider).subtotal;
});

final cartTotalProvider = Provider<double>((ref) {
  return ref.watch(cartProvider).total;
});

final cartIsEmptyProvider = Provider<bool>((ref) {
  return ref.watch(cartProvider).isEmpty;
});

final cartLoadingProvider = Provider<bool>((ref) {
  return ref.watch(cartProvider).isLoading;
});

final cartErrorProvider = Provider<String?>((ref) {
  return ref.watch(cartProvider).error;
});

// Check if specific product is in cart
final productInCartProvider = Provider.family<bool, int>((ref, productId) {
  return ref.watch(cartProvider.notifier).isProductInCart(productId);
});

// Get quantity of specific product in cart
final productQuantityInCartProvider = Provider.family<int, int>((
  ref,
  productId,
) {
  return ref.watch(cartProvider.notifier).getProductQuantity(productId);
});
