import 'dart:async';
import 'dart:math';
import 'package:hive_flutter/hive_flutter.dart';
import '../../shared/models/admin_model.dart';

class AdminAuthService {
  static final AdminAuthService _instance = AdminAuthService._internal();
  factory AdminAuthService() => _instance;
  AdminAuthService._internal();

  static const String _adminBoxName = 'admin_auth';
  static const String _adminTokenKey = 'admin_token';
  static const String _adminDataKey = 'admin_data';

  // Mock admin credentials
  static const Map<String, String> _mockCredentials = {
    '<EMAIL>': 'admin123',
    '<EMAIL>': 'manager123',
    '<EMAIL>': 'support123',
  };

  // Get current admin
  Future<Admin?> getCurrentAdmin() async {
    try {
      final box = await Hive.openBox(_adminBoxName);
      final adminData = box.get(_adminDataKey);
      if (adminData != null) {
        return Admin.fromJson(Map<String, dynamic>.from(adminData));
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  // Check if admin is logged in
  Future<bool> isLoggedIn() async {
    try {
      final box = await Hive.openBox(_adminBoxName);
      final token = box.get(_adminTokenKey);
      final adminData = box.get(_adminDataKey);
      return token != null && adminData != null;
    } catch (e) {
      return false;
    }
  }

  // Admin login
  Future<Admin?> login(String email, String password) async {
    try {
      // Simulate API delay
      await Future.delayed(const Duration(seconds: 1));

      // Check mock credentials
      if (_mockCredentials[email] != password) {
        return null;
      }

      // Create mock admin based on email
      final admin = _createMockAdmin(email);
      
      // Save to storage
      final box = await Hive.openBox(_adminBoxName);
      await box.put(_adminTokenKey, _generateToken());
      await box.put(_adminDataKey, admin.toJson());

      return admin;
    } catch (e) {
      return null;
    }
  }

  // Admin logout
  Future<bool> logout() async {
    try {
      final box = await Hive.openBox(_adminBoxName);
      await box.delete(_adminTokenKey);
      await box.delete(_adminDataKey);
      return true;
    } catch (e) {
      return false;
    }
  }

  // Update admin profile
  Future<Admin?> updateProfile({
    String? name,
    String? phoneNumber,
    String? avatar,
  }) async {
    try {
      final currentAdmin = await getCurrentAdmin();
      if (currentAdmin == null) return null;

      // Simulate API delay
      await Future.delayed(const Duration(milliseconds: 500));

      final updatedAdmin = currentAdmin.copyWith(
        name: name ?? currentAdmin.name,
        phoneNumber: phoneNumber ?? currentAdmin.phoneNumber,
        avatar: avatar ?? currentAdmin.avatar,
        updatedAt: DateTime.now(),
      );

      // Save to storage
      final box = await Hive.openBox(_adminBoxName);
      await box.put(_adminDataKey, updatedAdmin.toJson());

      return updatedAdmin;
    } catch (e) {
      return null;
    }
  }

  // Change password
  Future<bool> changePassword(String currentPassword, String newPassword) async {
    try {
      final admin = await getCurrentAdmin();
      if (admin == null) return false;

      // Simulate API delay
      await Future.delayed(const Duration(seconds: 1));

      // In real app, verify current password with backend
      // For now, just return success
      return true;
    } catch (e) {
      return false;
    }
  }

  // Helper methods
  String _generateToken() {
    final random = Random();
    final bytes = List<int>.generate(32, (i) => random.nextInt(256));
    return bytes.map((byte) => byte.toRadixString(16).padLeft(2, '0')).join();
  }

  Admin _createMockAdmin(String email) {
    final now = DateTime.now();
    
    switch (email) {
      case '<EMAIL>':
        return Admin(
          id: 1,
          name: 'أحمد المدير',
          email: email,
          phoneNumber: '+964 ************',
          role: AdminRoles.superAdmin,
          permissions: [
            AdminPermissions.manageProducts,
            AdminPermissions.manageOrders,
            AdminPermissions.manageUsers,
            AdminPermissions.viewReports,
            AdminPermissions.manageSettings,
            AdminPermissions.manageCategories,
            AdminPermissions.manageOffers,
            AdminPermissions.manageSupport,
          ],
          isActive: true,
          lastLogin: now,
          createdAt: now.subtract(const Duration(days: 30)),
          updatedAt: now,
        );
      
      case '<EMAIL>':
        return Admin(
          id: 2,
          name: 'سارة المديرة',
          email: email,
          phoneNumber: '+964 ************',
          role: AdminRoles.manager,
          permissions: [
            AdminPermissions.manageProducts,
            AdminPermissions.manageOrders,
            AdminPermissions.viewReports,
            AdminPermissions.manageCategories,
            AdminPermissions.manageOffers,
          ],
          isActive: true,
          lastLogin: now,
          createdAt: now.subtract(const Duration(days: 20)),
          updatedAt: now,
        );
      
      case '<EMAIL>':
        return Admin(
          id: 3,
          name: 'محمد الدعم',
          email: email,
          phoneNumber: '+964 ************',
          role: AdminRoles.support,
          permissions: [
            AdminPermissions.manageOrders,
            AdminPermissions.manageSupport,
          ],
          isActive: true,
          lastLogin: now,
          createdAt: now.subtract(const Duration(days: 10)),
          updatedAt: now,
        );
      
      default:
        throw Exception('Unknown admin email');
    }
  }
}
