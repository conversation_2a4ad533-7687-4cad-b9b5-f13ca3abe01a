<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="تطبيق عدستي - متجر العدسات اللاصقة">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="عدستي">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>عدستي - متجر العدسات اللاصقة</title>
  <link rel="manifest" href="manifest.json">

  <!-- Google Sign-In -->
  <script src="https://accounts.google.com/gsi/client" async defer></script>

  <!-- Facebook SDK -->
  <script async defer crossorigin="anonymous" src="https://connect.facebook.net/ar_AR/sdk.js"></script>

  <!-- Firebase -->
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-app-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-auth-compat.js"></script>
</head>
<body>
  <script>
    // Firebase configuration (Demo keys - replace with real ones)
    const firebaseConfig = {
      apiKey: "AIzaSyDemoKeyForWebPlatform",
      authDomain: "visionlens-demo.firebaseapp.com",
      projectId: "visionlens-demo",
      storageBucket: "visionlens-demo.appspot.com",
      messagingSenderId: "*********",
      appId: "1:*********:web:abcdef123456",
      measurementId: "G-XXXXXXXXXX"
    };

    // Initialize Firebase
    firebase.initializeApp(firebaseConfig);

    // Facebook SDK initialization
    window.fbAsyncInit = function() {
      FB.init({
        appId: '*********012345', // Replace with your Facebook App ID
        cookie: true,
        xfbml: true,
        version: 'v18.0'
      });
    };
  </script>

  <script src="flutter_bootstrap.js" async></script>
</body>
</html>
