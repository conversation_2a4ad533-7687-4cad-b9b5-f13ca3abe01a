import 'package:json_annotation/json_annotation.dart';
import 'product_model.dart';
import 'user_model.dart';

part 'order_model.g.dart';

@JsonSerializable()
class Order {
  final int id;
  @J<PERSON><PERSON><PERSON>(name: 'order_number')
  final String orderNumber;
  final User user;
  final String status;
  @Json<PERSON><PERSON>(name: 'payment_status')
  final String paymentStatus;
  @JsonKey(name: 'shipping_name')
  final String shippingName;
  @JsonKey(name: 'shipping_phone')
  final String shippingPhone;
  @<PERSON>sonKey(name: 'shipping_address')
  final String shippingAddress;
  @<PERSON>son<PERSON>ey(name: 'shipping_city')
  final String shippingCity;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'shipping_postal_code')
  final String? shippingPostalCode;
  final double subtotal;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'shipping_cost')
  final double shippingCost;
  @JsonKey(name: 'tax_amount')
  final double taxAmount;
  @Json<PERSON>ey(name: 'total_amount')
  final double totalAmount;
  final String? notes;
  @<PERSON>son<PERSON><PERSON>(name: 'created_at')
  final DateTime createdAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at')
  final DateTime updatedAt;
  final List<OrderItem> items;

  const Order({
    required this.id,
    required this.orderNumber,
    required this.user,
    required this.status,
    required this.paymentStatus,
    required this.shippingName,
    required this.shippingPhone,
    required this.shippingAddress,
    required this.shippingCity,
    this.shippingPostalCode,
    required this.subtotal,
    required this.shippingCost,
    required this.taxAmount,
    required this.totalAmount,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
    required this.items,
  });

  factory Order.fromJson(Map<String, dynamic> json) => _$OrderFromJson(json);
  Map<String, dynamic> toJson() => _$OrderToJson(this);

  String get statusArabic {
    const statusMap = {
      'pending': 'في الانتظار',
      'confirmed': 'مؤكد',
      'processing': 'قيد التحضير',
      'shipped': 'تم الشحن',
      'delivered': 'تم التسليم',
      'cancelled': 'ملغي',
      'returned': 'مرتجع',
    };
    return statusMap[status] ?? status;
  }

  String get paymentStatusArabic {
    const statusMap = {
      'pending': 'في الانتظار',
      'paid': 'مدفوع',
      'failed': 'فشل',
      'refunded': 'مسترد',
    };
    return statusMap[paymentStatus] ?? paymentStatus;
  }

  bool get canBeCancelled {
    return status == 'pending' || status == 'confirmed';
  }

  bool get isDelivered {
    return status == 'delivered';
  }

  bool get canBeReordered {
    return status == 'delivered' || status == 'cancelled';
  }

  int get totalItems {
    return items.fold(0, (sum, item) => sum + item.quantity);
  }

  Order copyWith({
    int? id,
    String? orderNumber,
    User? user,
    String? status,
    String? paymentStatus,
    String? shippingName,
    String? shippingPhone,
    String? shippingAddress,
    String? shippingCity,
    String? shippingPostalCode,
    double? subtotal,
    double? shippingCost,
    double? taxAmount,
    double? totalAmount,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<OrderItem>? items,
  }) {
    return Order(
      id: id ?? this.id,
      orderNumber: orderNumber ?? this.orderNumber,
      user: user ?? this.user,
      status: status ?? this.status,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      shippingName: shippingName ?? this.shippingName,
      shippingPhone: shippingPhone ?? this.shippingPhone,
      shippingAddress: shippingAddress ?? this.shippingAddress,
      shippingCity: shippingCity ?? this.shippingCity,
      shippingPostalCode: shippingPostalCode ?? this.shippingPostalCode,
      subtotal: subtotal ?? this.subtotal,
      shippingCost: shippingCost ?? this.shippingCost,
      taxAmount: taxAmount ?? this.taxAmount,
      totalAmount: totalAmount ?? this.totalAmount,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      items: items ?? this.items,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Order && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Order(id: $id, orderNumber: $orderNumber, status: $status, total: $totalAmount)';
  }
}

@JsonSerializable()
class OrderItem {
  final int id;
  final int orderId;
  final Product product;
  final int quantity;
  final double price;
  final double total;

  const OrderItem({
    required this.id,
    required this.orderId,
    required this.product,
    required this.quantity,
    required this.price,
    required this.total,
  });

  factory OrderItem.fromJson(Map<String, dynamic> json) => _$OrderItemFromJson(json);
  Map<String, dynamic> toJson() => _$OrderItemToJson(this);

  OrderItem copyWith({
    int? id,
    int? orderId,
    Product? product,
    int? quantity,
    double? price,
    double? total,
  }) {
    return OrderItem(
      id: id ?? this.id,
      orderId: orderId ?? this.orderId,
      product: product ?? this.product,
      quantity: quantity ?? this.quantity,
      price: price ?? this.price,
      total: total ?? this.total,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is OrderItem && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'OrderItem(id: $id, product: ${product.name}, quantity: $quantity, total: $total)';
  }
}

@JsonSerializable()
class CreateOrderRequest {
  @JsonKey(name: 'shipping_name')
  final String shippingName;
  @JsonKey(name: 'shipping_phone')
  final String shippingPhone;
  @JsonKey(name: 'shipping_address')
  final String shippingAddress;
  @JsonKey(name: 'shipping_city')
  final String shippingCity;
  @JsonKey(name: 'shipping_postal_code')
  final String? shippingPostalCode;
  @JsonKey(name: 'payment_method')
  final String paymentMethod;
  final String? notes;

  const CreateOrderRequest({
    required this.shippingName,
    required this.shippingPhone,
    required this.shippingAddress,
    required this.shippingCity,
    this.shippingPostalCode,
    required this.paymentMethod,
    this.notes,
  });

  factory CreateOrderRequest.fromJson(Map<String, dynamic> json) => _$CreateOrderRequestFromJson(json);
  Map<String, dynamic> toJson() => _$CreateOrderRequestToJson(this);
}

@JsonSerializable()
class OrdersResponse {
  final List<Order> orders;
  final int total;
  final int page;
  @JsonKey(name: 'per_page')
  final int perPage;
  @JsonKey(name: 'total_pages')
  final int totalPages;

  const OrdersResponse({
    required this.orders,
    required this.total,
    required this.page,
    required this.perPage,
    required this.totalPages,
  });

  factory OrdersResponse.fromJson(Map<String, dynamic> json) => _$OrdersResponseFromJson(json);
  Map<String, dynamic> toJson() => _$OrdersResponseToJson(this);

  bool get hasNextPage => page < totalPages;
  bool get hasPreviousPage => page > 1;
}

@JsonSerializable()
class OrderTrackingInfo {
  @JsonKey(name: 'order_id')
  final int orderId;
  final String status;
  @JsonKey(name: 'tracking_number')
  final String? trackingNumber;
  @JsonKey(name: 'estimated_delivery')
  final DateTime? estimatedDelivery;
  @JsonKey(name: 'status_history')
  final List<OrderStatusHistory> statusHistory;

  const OrderTrackingInfo({
    required this.orderId,
    required this.status,
    this.trackingNumber,
    this.estimatedDelivery,
    required this.statusHistory,
  });

  factory OrderTrackingInfo.fromJson(Map<String, dynamic> json) => _$OrderTrackingInfoFromJson(json);
  Map<String, dynamic> toJson() => _$OrderTrackingInfoToJson(this);
}

@JsonSerializable()
class OrderStatusHistory {
  final String status;
  final String description;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;

  const OrderStatusHistory({
    required this.status,
    required this.description,
    required this.createdAt,
  });

  factory OrderStatusHistory.fromJson(Map<String, dynamic> json) => _$OrderStatusHistoryFromJson(json);
  Map<String, dynamic> toJson() => _$OrderStatusHistoryToJson(this);

  String get statusArabic {
    const statusMap = {
      'pending': 'في الانتظار',
      'confirmed': 'مؤكد',
      'processing': 'قيد التحضير',
      'shipped': 'تم الشحن',
      'delivered': 'تم التسليم',
      'cancelled': 'ملغي',
      'returned': 'مرتجع',
    };
    return statusMap[status] ?? status;
  }
}
