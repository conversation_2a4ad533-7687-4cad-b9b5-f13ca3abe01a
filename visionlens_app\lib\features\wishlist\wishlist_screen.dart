import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../shared/widgets/custom_app_bar.dart';
import '../../shared/widgets/product_card.dart';
import 'widgets/empty_wishlist.dart';

class WishlistScreen extends ConsumerStatefulWidget {
  const WishlistScreen({super.key});

  @override
  ConsumerState<WishlistScreen> createState() => _WishlistScreenState();
}

class _WishlistScreenState extends ConsumerState<WishlistScreen> {
  List<Map<String, dynamic>> _wishlistItems = [];
  bool _isGridView = true;

  @override
  void initState() {
    super.initState();
    _loadWishlistItems();
  }

  void _loadWishlistItems() {
    // TODO: Load wishlist items from provider/storage
    setState(() {
      _wishlistItems = _getDummyWishlistItems();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: CustomAppBar(
        title: 'المفضلة',
        showBackButton: false,
        showCartIcon: true,
        actions: [
          if (_wishlistItems.isNotEmpty) ...[
            IconButton(
              icon: Icon(
                _isGridView ? Icons.list : Icons.grid_view,
                color: AppColors.textSecondary,
              ),
              onPressed: () {
                setState(() {
                  _isGridView = !_isGridView;
                });
              },
            ),
            PopupMenuButton<String>(
              icon: const Icon(
                Icons.more_vert,
                color: AppColors.textSecondary,
              ),
              onSelected: (value) {
                switch (value) {
                  case 'clear_all':
                    _showClearWishlistDialog();
                    break;
                  case 'add_all_to_cart':
                    _addAllToCart();
                    break;
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'add_all_to_cart',
                  child: Row(
                    children: [
                      Icon(Icons.shopping_cart, color: AppColors.primary),
                      SizedBox(width: 8),
                      Text('إضافة الكل للسلة'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'clear_all',
                  child: Row(
                    children: [
                      Icon(Icons.clear_all, color: AppColors.error),
                      SizedBox(width: 8),
                      Text('مسح الكل'),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
      body: _wishlistItems.isEmpty ? _buildEmptyWishlist() : _buildWishlistContent(),
    );
  }

  Widget _buildEmptyWishlist() {
    return const EmptyWishlist();
  }

  Widget _buildWishlistContent() {
    return RefreshIndicator(
      onRefresh: _onRefresh,
      child: Column(
        children: [
          // Header with count
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${_wishlistItems.length} منتج في المفضلة',
                  style: AppTextStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                TextButton.icon(
                  onPressed: _addAllToCart,
                  icon: const Icon(Icons.shopping_cart),
                  label: const Text('إضافة الكل للسلة'),
                  style: TextButton.styleFrom(
                    foregroundColor: AppColors.primary,
                  ),
                ),
              ],
            ),
          ),
          
          // Products Grid/List
          Expanded(
            child: _isGridView ? _buildGridView() : _buildListView(),
          ),
        ],
      ),
    );
  }

  Widget _buildGridView() {
    return GridView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.7,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: _wishlistItems.length,
      itemBuilder: (context, index) {
        final item = _wishlistItems[index];
        return ProductCard(
          imageUrl: item['image'],
          name: item['name'],
          price: item['price'],
          originalPrice: item['originalPrice'],
          rating: item['rating'],
          isInStock: item['isInStock'],
          isFavorite: true,
          width: double.infinity,
          onTap: () {
            _navigateToProductDetails(item);
          },
          onAddToCart: () {
            _addToCart(item, index);
          },
          onToggleFavorite: () {
            _removeFromWishlist(index);
          },
        );
      },
    );
  }

  Widget _buildListView() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _wishlistItems.length,
      itemBuilder: (context, index) {
        final item = _wishlistItems[index];
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: AppColors.shadow,
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              // Product Image
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: AppColors.surfaceVariant,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: item['image'] != null
                      ? Image.network(
                          item['image'],
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return const Icon(
                              Icons.image_not_supported,
                              color: AppColors.textSecondary,
                            );
                          },
                        )
                      : const Icon(
                          Icons.image_not_supported,
                          color: AppColors.textSecondary,
                        ),
                ),
              ),
              
              const SizedBox(width: 12),
              
              // Product Details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item['name'],
                      style: AppTextStyles.titleSmall.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    const SizedBox(height: 8),
                    
                    // Price
                    Row(
                      children: [
                        Text(
                          '${item['price'].toStringAsFixed(0)} د.ع',
                          style: AppTextStyles.priceRegular.copyWith(
                            fontSize: 16,
                          ),
                        ),
                        if (item['originalPrice'] != null) ...[
                          const SizedBox(width: 8),
                          Text(
                            '${item['originalPrice'].toStringAsFixed(0)} د.ع',
                            style: AppTextStyles.priceDiscounted.copyWith(
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ],
                    ),
                    
                    const SizedBox(height: 8),
                    
                    // Stock Status
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: item['isInStock'] 
                            ? AppColors.success.withOpacity(0.1)
                            : AppColors.error.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        item['isInStock'] ? 'متوفر' : 'نفد المخزون',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: item['isInStock'] ? AppColors.success : AppColors.error,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              
              // Action Buttons
              Column(
                children: [
                  IconButton(
                    onPressed: () => _addToCart(item, index),
                    icon: const Icon(Icons.shopping_cart),
                    style: IconButton.styleFrom(
                      backgroundColor: AppColors.primary.withOpacity(0.1),
                      foregroundColor: AppColors.primary,
                    ),
                  ),
                  IconButton(
                    onPressed: () => _removeFromWishlist(index),
                    icon: const Icon(Icons.favorite),
                    style: IconButton.styleFrom(
                      backgroundColor: AppColors.error.withOpacity(0.1),
                      foregroundColor: AppColors.error,
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  void _navigateToProductDetails(Map<String, dynamic> item) {
    // TODO: Navigate to product details screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('عرض تفاصيل ${item['name']}'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _addToCart(Map<String, dynamic> item, int index) {
    if (!item['isInStock']) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('هذا المنتج غير متوفر حالياً'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    // TODO: Add to cart logic
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم إضافة ${item['name']} إلى السلة'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _removeFromWishlist(int index) {
    final item = _wishlistItems[index];
    setState(() {
      _wishlistItems.removeAt(index);
    });
    
    // TODO: Remove from wishlist in provider/storage
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم حذف ${item['name']} من المفضلة'),
        backgroundColor: AppColors.success,
        action: SnackBarAction(
          label: 'تراجع',
          onPressed: () {
            setState(() {
              _wishlistItems.insert(index, item);
            });
          },
        ),
      ),
    );
  }

  void _addAllToCart() {
    final availableItems = _wishlistItems.where((item) => item['isInStock']).toList();
    
    if (availableItems.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا توجد منتجات متوفرة لإضافتها للسلة'),
          backgroundColor: AppColors.warning,
        ),
      );
      return;
    }

    // TODO: Add all available items to cart
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم إضافة ${availableItems.length} منتج إلى السلة'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _showClearWishlistDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('مسح المفضلة'),
          content: const Text('هل أنت متأكد من رغبتك في مسح جميع المنتجات من المفضلة؟'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _clearWishlist();
              },
              style: TextButton.styleFrom(
                foregroundColor: AppColors.error,
              ),
              child: const Text('مسح'),
            ),
          ],
        );
      },
    );
  }

  void _clearWishlist() {
    setState(() {
      _wishlistItems.clear();
    });
    
    // TODO: Clear wishlist in provider/storage
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم مسح المفضلة بنجاح'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  Future<void> _onRefresh() async {
    // TODO: Refresh wishlist data
    await Future.delayed(const Duration(seconds: 1));
    _loadWishlistItems();
  }

  List<Map<String, dynamic>> _getDummyWishlistItems() {
    return [
      {
        'name': 'عدسات أكيوفيو يومية',
        'price': 25000.0,
        'originalPrice': 30000.0,
        'rating': 4.5,
        'image': 'https://via.placeholder.com/200x200',
        'isInStock': true,
      },
      {
        'name': 'عدسات بايوفينيتي شهرية',
        'price': 45000.0,
        'originalPrice': null,
        'rating': 4.8,
        'image': 'https://via.placeholder.com/200x200',
        'isInStock': true,
      },
      {
        'name': 'عدسات فريش لوك ملونة',
        'price': 35000.0,
        'originalPrice': 40000.0,
        'rating': 4.3,
        'image': 'https://via.placeholder.com/200x200',
        'isInStock': false,
      },
    ];
  }
}
