class AppConstants {
  // App Info
  static const String appName = 'عدستي - VisionLens';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'متجر العدسات اللاصقة';
  
  // API Configuration
  static const String baseUrl = 'https://api.visionlens.com';
  static const String apiVersion = '/api/v1';
  static const String apiUrl = '$baseUrl$apiVersion';
  
  // Storage Keys
  static const String userTokenKey = 'user_token';
  static const String userDataKey = 'user_data';
  static const String languageKey = 'language';
  static const String themeKey = 'theme';
  static const String cartKey = 'cart_items';
  static const String wishlistKey = 'wishlist_items';
  static const String addressesKey = 'user_addresses';
  static const String onboardingKey = 'onboarding_completed';
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 50;
  
  // Image Configuration
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'webp'];
  
  // Validation
  static const int minPasswordLength = 8;
  static const int maxPasswordLength = 50;
  static const int minUsernameLength = 3;
  static const int maxUsernameLength = 30;
  
  // Currency
  static const String currency = 'IQD';
  static const String currencySymbol = 'د.ع';
  
  // Shipping
  static const double freeShippingThreshold = 50000; // 50,000 IQD
  static const double defaultShippingCost = 5000; // 5,000 IQD
  
  // Contact Info
  static const String supportEmail = '<EMAIL>';
  static const String supportPhone = '+964 ************';
  static const String whatsappNumber = '+964 ************';
  
  // Social Media
  static const String facebookUrl = 'https://facebook.com/visionlens';
  static const String instagramUrl = 'https://instagram.com/visionlens';
  static const String twitterUrl = 'https://twitter.com/visionlens';
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);
  
  // Timeouts
  static const Duration connectionTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  static const Duration sendTimeout = Duration(seconds: 30);
  
  // Cache
  static const Duration cacheExpiry = Duration(hours: 24);
  static const int maxCacheSize = 100 * 1024 * 1024; // 100MB
  
  // Notification Channels
  static const String orderNotificationChannel = 'order_notifications';
  static const String promotionNotificationChannel = 'promotion_notifications';
  static const String generalNotificationChannel = 'general_notifications';
  
  // Deep Links
  static const String deepLinkScheme = 'visionlens';
  static const String deepLinkHost = 'app';
  
  // Error Messages
  static const String networkErrorMessage = 'خطأ في الاتصال بالإنترنت';
  static const String serverErrorMessage = 'خطأ في الخادم، يرجى المحاولة لاحقاً';
  static const String unknownErrorMessage = 'حدث خطأ غير متوقع';
  static const String validationErrorMessage = 'يرجى التحقق من البيانات المدخلة';
  
  // Success Messages
  static const String loginSuccessMessage = 'تم تسجيل الدخول بنجاح';
  static const String registerSuccessMessage = 'تم إنشاء الحساب بنجاح';
  static const String orderSuccessMessage = 'تم إرسال الطلب بنجاح';
  static const String updateSuccessMessage = 'تم التحديث بنجاح';
  
  // Product Categories
  static const List<String> lensTypes = [
    'يومية',
    'أسبوعية', 
    'شهرية',
    'سنوية'
  ];
  
  static const List<String> lensUsage = [
    'طبية',
    'تجميلية',
    'طبية وتجميلية'
  ];
  
  static const List<String> lensColors = [
    'شفاف',
    'أزرق',
    'أخضر',
    'بني',
    'رمادي',
    'عسلي',
    'أسود'
  ];
  
  // Order Status
  static const List<String> orderStatuses = [
    'pending',
    'confirmed',
    'processing',
    'shipped',
    'delivered',
    'cancelled',
    'returned'
  ];
  
  static const Map<String, String> orderStatusArabic = {
    'pending': 'في الانتظار',
    'confirmed': 'مؤكد',
    'processing': 'قيد التحضير',
    'shipped': 'تم الشحن',
    'delivered': 'تم التسليم',
    'cancelled': 'ملغي',
    'returned': 'مرتجع'
  };
  
  // Payment Methods
  static const List<String> paymentMethods = [
    'cash_on_delivery',
    'credit_card',
    'bank_transfer',
    'digital_wallet'
  ];
  
  static const Map<String, String> paymentMethodsArabic = {
    'cash_on_delivery': 'الدفع عند التسليم',
    'credit_card': 'بطاقة ائتمانية',
    'bank_transfer': 'تحويل بنكي',
    'digital_wallet': 'محفظة رقمية'
  };
  
  // Regex Patterns
  static const String emailPattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  static const String phonePattern = r'^(\+964|0)?7[0-9]{9}$';
  static const String passwordPattern = r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$';
  
  // File Paths
  static const String logoPath = 'assets/logos/logo.png';
  static const String splashLogoPath = 'assets/logos/splash_logo.png';
  static const String placeholderImagePath = 'assets/images/placeholder.png';
  static const String noImagePath = 'assets/images/no_image.png';
  
  // Animation Paths
  static const String loadingAnimationPath = 'assets/animations/loading.json';
  static const String successAnimationPath = 'assets/animations/success.json';
  static const String errorAnimationPath = 'assets/animations/error.json';
  static const String emptyCartAnimationPath = 'assets/animations/empty_cart.json';
  static const String noInternetAnimationPath = 'assets/animations/no_internet.json';
}
