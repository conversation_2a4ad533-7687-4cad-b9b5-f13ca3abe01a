name: visionlens_app
description: "تطبيق عدستي - متجر العدسات اللاصقة"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # UI Components
  cupertino_icons: ^1.0.8
  material_design_icons_flutter: ^7.0.7296
  flutter_svg: ^2.0.10+1
  cached_network_image: ^3.3.1
  photo_view: ^0.15.0
  carousel_slider: ^4.2.1
  flutter_staggered_grid_view: ^0.7.0
  shimmer: ^3.0.0
  lottie: ^3.1.2

  # Navigation
  go_router: ^14.2.7

  # State Management
  provider: ^6.1.2
  riverpod: ^2.5.1
  flutter_riverpod: ^2.5.1

  # Network & API
  dio: ^5.4.3+1
  retrofit: ^4.1.0
  json_annotation: ^4.9.0

  # Storage
  shared_preferences: ^2.2.3
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # Authentication & Security
  local_auth: ^2.2.0
  crypto: ^3.0.3

  # Location & Maps
  geolocator: ^12.0.0
  geocoding: ^3.0.0
  google_maps_flutter: ^2.7.0

  # Notifications
  firebase_core: ^3.3.0
  firebase_messaging: ^15.0.4
  flutter_local_notifications: ^17.2.2

  # Image Handling
  image_picker: ^1.1.2
  image_cropper: ^8.0.1

  # Utils
  intl: ^0.20.2
  url_launcher: ^6.3.0
  share_plus: ^10.0.2
  connectivity_plus: ^6.0.5
  device_info_plus: ^10.1.2
  package_info_plus: ^8.0.2

  # Payment
  flutter_stripe: ^11.1.0

  # Camera & AR (for future features)
  camera: ^0.11.0+2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

  # Code Generation
  build_runner: ^2.4.12
  retrofit_generator: ^8.1.2
  json_serializable: ^6.8.0
  hive_generator: ^2.0.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/logos/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # Custom Fonts will be added later
  # fonts:
