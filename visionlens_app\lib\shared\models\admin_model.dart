import 'package:json_annotation/json_annotation.dart';

part 'admin_model.g.dart';

@JsonSerializable()
class Admin {
  final int id;
  final String name;
  final String email;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'phone_number')
  final String? phoneNumber;
  final String role;
  final List<String> permissions;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_active')
  final bool isActive;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'last_login')
  final DateTime? lastLogin;
  @<PERSON>son<PERSON><PERSON>(name: 'created_at')
  final DateTime createdAt;
  @Json<PERSON>ey(name: 'updated_at')
  final DateTime updatedAt;
  final String? avatar;

  const Admin({
    required this.id,
    required this.name,
    required this.email,
    this.phoneNumber,
    required this.role,
    required this.permissions,
    required this.isActive,
    this.lastLogin,
    required this.createdAt,
    required this.updatedAt,
    this.avatar,
  });

  factory Admin.fromJson(Map<String, dynamic> json) => _$AdminFromJson(json);
  Map<String, dynamic> toJson() => _$AdminToJson(this);

  Admin copyWith({
    int? id,
    String? name,
    String? email,
    String? phoneNumber,
    String? role,
    List<String>? permissions,
    bool? isActive,
    DateTime? lastLogin,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? avatar,
  }) {
    return Admin(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      role: role ?? this.role,
      permissions: permissions ?? this.permissions,
      isActive: isActive ?? this.isActive,
      lastLogin: lastLogin ?? this.lastLogin,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      avatar: avatar ?? this.avatar,
    );
  }

  // Helper methods
  bool hasPermission(String permission) {
    return permissions.contains(permission) || role == 'super_admin';
  }

  bool get canManageProducts => hasPermission('manage_products');
  bool get canManageOrders => hasPermission('manage_orders');
  bool get canManageUsers => hasPermission('manage_users');
  bool get canViewReports => hasPermission('view_reports');
  bool get canManageSettings => hasPermission('manage_settings');
}

// Admin Roles
class AdminRoles {
  static const String superAdmin = 'super_admin';
  static const String admin = 'admin';
  static const String manager = 'manager';
  static const String support = 'support';
}

// Admin Permissions
class AdminPermissions {
  static const String manageProducts = 'manage_products';
  static const String manageOrders = 'manage_orders';
  static const String manageUsers = 'manage_users';
  static const String viewReports = 'view_reports';
  static const String manageSettings = 'manage_settings';
  static const String manageCategories = 'manage_categories';
  static const String manageOffers = 'manage_offers';
  static const String manageSupport = 'manage_support';
}
