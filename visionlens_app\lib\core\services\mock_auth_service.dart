import 'dart:async';
import 'dart:math';
import '../constants/app_constants.dart';
import '../../shared/models/user_model.dart' as app_models;

class MockAuthService {
  static final MockAuthService _instance = MockAuthService._internal();
  factory MockAuthService() => _instance;
  MockAuthService._internal();

  // Mock current user
  app_models.User? _currentUser;
  
  // Auth state stream controller
  final StreamController<app_models.User?> _authStateController = 
      StreamController<app_models.User?>.broadcast();

  // Get current user
  app_models.User? get currentUser => _currentUser;
  
  // Auth state stream
  Stream<app_models.User?> get authStateChanges => _authStateController.stream;

  // Email & Password Authentication
  Future<app_models.User?> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      // Simulate API call delay
      await Future.delayed(const Duration(seconds: 1));
      
      // Simple validation
      if (email.isEmpty || password.isEmpty) {
        throw Exception('يرجى إدخال البريد الإلكتروني وكلمة المرور');
      }
      
      if (!email.contains('@')) {
        throw Exception('البريد الإلكتروني غير صحيح');
      }
      
      if (password.length < 6) {
        throw Exception('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
      }
      
      // Create mock user
      final user = app_models.User(
        id: email.hashCode,
        username: email.split('@')[0],
        firstName: 'أحمد',
        lastName: 'محمد',
        email: email,
        isActive: true,
        dateJoined: DateTime.now(),
        profile: const app_models.UserProfile(
          phone: '+964 ************',
          isEmailVerified: true,
        ),
      );
      
      _currentUser = user;
      _authStateController.add(user);
      
      return user;
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  Future<app_models.User?> createUserWithEmailAndPassword({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    String? phone,
  }) async {
    try {
      // Simulate API call delay
      await Future.delayed(const Duration(seconds: 2));
      
      // Simple validation
      if (email.isEmpty || password.isEmpty || firstName.isEmpty || lastName.isEmpty) {
        throw Exception('يرجى ملء جميع الحقول المطلوبة');
      }
      
      if (!email.contains('@')) {
        throw Exception('البريد الإلكتروني غير صحيح');
      }
      
      if (password.length < 6) {
        throw Exception('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
      }
      
      // Create new user
      final user = app_models.User(
        id: DateTime.now().millisecondsSinceEpoch,
        username: email.split('@')[0],
        firstName: firstName,
        lastName: lastName,
        email: email,
        isActive: true,
        dateJoined: DateTime.now(),
        profile: app_models.UserProfile(
          phone: phone,
          isEmailVerified: false, // Needs verification
        ),
      );
      
      _currentUser = user;
      _authStateController.add(user);
      
      return user;
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  // Google Sign In (Mock)
  Future<app_models.User?> signInWithGoogle() async {
    try {
      // Simulate API call delay
      await Future.delayed(const Duration(seconds: 1));
      
      // Create mock Google user
      final user = app_models.User(
        id: Random().nextInt(100000),
        username: 'google_user',
        firstName: 'مستخدم',
        lastName: 'Google',
        email: '<EMAIL>',
        isActive: true,
        dateJoined: DateTime.now(),
        profile: const app_models.UserProfile(
          isEmailVerified: true,
          photoUrl: 'https://via.placeholder.com/150',
        ),
      );
      
      _currentUser = user;
      _authStateController.add(user);
      
      return user;
    } catch (e) {
      throw Exception('خطأ في تسجيل الدخول بـ Google: ${e.toString()}');
    }
  }

  // Facebook Sign In (Mock)
  Future<app_models.User?> signInWithFacebook() async {
    try {
      // Simulate API call delay
      await Future.delayed(const Duration(seconds: 1));
      
      // Create mock Facebook user
      final user = app_models.User(
        id: Random().nextInt(100000),
        username: 'facebook_user',
        firstName: 'مستخدم',
        lastName: 'Facebook',
        email: '<EMAIL>',
        isActive: true,
        dateJoined: DateTime.now(),
        profile: const app_models.UserProfile(
          isEmailVerified: true,
          photoUrl: 'https://via.placeholder.com/150',
        ),
      );
      
      _currentUser = user;
      _authStateController.add(user);
      
      return user;
    } catch (e) {
      throw Exception('خطأ في تسجيل الدخول بـ Facebook: ${e.toString()}');
    }
  }

  // Apple Sign In (Mock)
  Future<app_models.User?> signInWithApple() async {
    try {
      // Simulate API call delay
      await Future.delayed(const Duration(seconds: 1));
      
      // Create mock Apple user
      final user = app_models.User(
        id: Random().nextInt(100000),
        username: 'apple_user',
        firstName: 'مستخدم',
        lastName: 'Apple',
        email: '<EMAIL>',
        isActive: true,
        dateJoined: DateTime.now(),
        profile: const app_models.UserProfile(
          isEmailVerified: true,
        ),
      );
      
      _currentUser = user;
      _authStateController.add(user);
      
      return user;
    } catch (e) {
      throw Exception('خطأ في تسجيل الدخول بـ Apple: ${e.toString()}');
    }
  }

  // Password Reset
  Future<void> sendPasswordResetEmail(String email) async {
    try {
      // Simulate API call delay
      await Future.delayed(const Duration(seconds: 1));
      
      if (email.isEmpty || !email.contains('@')) {
        throw Exception('البريد الإلكتروني غير صحيح');
      }
      
      // Mock success - in real app, this would send an email
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  // Sign Out
  Future<void> signOut() async {
    try {
      _currentUser = null;
      _authStateController.add(null);
    } catch (e) {
      throw Exception('خطأ في تسجيل الخروج: ${e.toString()}');
    }
  }

  // Update Profile
  Future<app_models.User?> updateProfile({
    String? firstName,
    String? lastName,
    String? phone,
  }) async {
    try {
      if (_currentUser == null) {
        throw Exception('لا يوجد مستخدم مسجل دخول');
      }
      
      // Simulate API call delay
      await Future.delayed(const Duration(seconds: 1));
      
      final updatedProfile = _currentUser!.profile?.copyWith(
        phone: phone ?? _currentUser!.profile?.phone,
      );
      
      final updatedUser = _currentUser!.copyWith(
        firstName: firstName ?? _currentUser!.firstName,
        lastName: lastName ?? _currentUser!.lastName,
        profile: updatedProfile,
      );
      
      _currentUser = updatedUser;
      _authStateController.add(updatedUser);
      
      return updatedUser;
    } catch (e) {
      throw Exception('خطأ في تحديث الملف الشخصي: ${e.toString()}');
    }
  }

  // Dispose
  void dispose() {
    _authStateController.close();
  }
}
