import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/providers/admin_auth_provider.dart';

class AdminSidebar extends ConsumerWidget {
  final int selectedIndex;
  final bool isExpanded;
  final Function(int) onItemSelected;
  final VoidCallback onToggleExpanded;

  const AdminSidebar({
    super.key,
    required this.selectedIndex,
    required this.isExpanded,
    required this.onItemSelected,
    required this.onToggleExpanded,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final admin = ref.watch(currentAdminProvider);

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      width: isExpanded ? 280 : 80,
      decoration: BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 8,
            offset: const Offset(2, 0),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header
          _buildHeader(),
          
          // Menu Items
          Expanded(
            child: _buildMenuItems(admin),
          ),
          
          // Toggle Button
          _buildToggleButton(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      height: 70,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: AppColors.primary,
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.3),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.admin_panel_settings,
              color: Colors.white,
              size: 24,
            ),
          ),
          if (isExpanded) ...[
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'لوحة التحكم',
                    style: AppTextStyles.labelLarge.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'VisionLens Admin',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: Colors.white.withValues(alpha: 0.8),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMenuItems(admin) {
    final menuItems = [
      _MenuItem(
        icon: Icons.dashboard_outlined,
        activeIcon: Icons.dashboard,
        title: 'لوحة التحكم',
        index: 0,
      ),
      _MenuItem(
        icon: Icons.inventory_2_outlined,
        activeIcon: Icons.inventory_2,
        title: 'إدارة المنتجات',
        index: 1,
        permission: 'manage_products',
      ),
      _MenuItem(
        icon: Icons.shopping_cart_outlined,
        activeIcon: Icons.shopping_cart,
        title: 'إدارة الطلبات',
        index: 2,
        permission: 'manage_orders',
      ),
      _MenuItem(
        icon: Icons.people_outline,
        activeIcon: Icons.people,
        title: 'إدارة العملاء',
        index: 3,
        permission: 'manage_users',
      ),
      _MenuItem(
        icon: Icons.analytics_outlined,
        activeIcon: Icons.analytics,
        title: 'التقارير',
        index: 4,
        permission: 'view_reports',
      ),
      _MenuItem(
        icon: Icons.settings_outlined,
        activeIcon: Icons.settings,
        title: 'الإعدادات',
        index: 5,
        permission: 'manage_settings',
      ),
    ];

    return ListView(
      padding: const EdgeInsets.symmetric(vertical: 8),
      children: menuItems.map((item) {
        // Check permissions
        if (item.permission != null && admin != null) {
          if (!admin.hasPermission(item.permission!)) {
            return const SizedBox.shrink();
          }
        }

        final isSelected = selectedIndex == item.index;
        
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () => onItemSelected(item.index),
              borderRadius: BorderRadius.circular(12),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: isSelected 
                      ? AppColors.primary.withValues(alpha: 0.1)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(12),
                  border: isSelected
                      ? Border.all(color: AppColors.primary.withValues(alpha: 0.3))
                      : null,
                ),
                child: Row(
                  children: [
                    Icon(
                      isSelected ? item.activeIcon : item.icon,
                      color: isSelected ? AppColors.primary : AppColors.textSecondary,
                      size: 24,
                    ),
                    if (isExpanded) ...[
                      const SizedBox(width: 16),
                      Expanded(
                        child: Text(
                          item.title,
                          style: AppTextStyles.labelMedium.copyWith(
                            color: isSelected ? AppColors.primary : AppColors.textPrimary,
                            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildToggleButton() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onToggleExpanded,
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.textSecondary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  isExpanded ? Icons.chevron_left : Icons.chevron_right,
                  color: AppColors.textSecondary,
                ),
                if (isExpanded) ...[
                  const SizedBox(width: 8),
                  Text(
                    'طي القائمة',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _MenuItem {
  final IconData icon;
  final IconData activeIcon;
  final String title;
  final int index;
  final String? permission;

  const _MenuItem({
    required this.icon,
    required this.activeIcon,
    required this.title,
    required this.index,
    this.permission,
  });
}
