# عدستي - VisionLens 👁️

تطبيق متجر العدسات اللاصقة الأول في العراق، مبني بـ Flutter مع دعم كامل للغة العربية.

## 📱 لقطات الشاشة

*سيتم إضافة لقطات الشاشة قريباً*

## ✨ المميزات

### 🏠 الشاشة الرئيسية
- عرض البانرات الترويجية
- الفئات الرئيسية للعدسات
- المنتجات المميزة والأكثر مبيعاً
- أحدث المنتجات
- نصائح العناية بالعدسات

### 🛍️ التسوق
- تصفح المنتجات بالفئات
- البحث المتقدم مع الفلاتر
- تفاصيل المنتج الشاملة
- إضافة المنتجات للسلة والمفضلة
- نظام التقييمات والمراجعات

### 🛒 سلة التسوق
- إدارة المنتجات في السلة
- حساب التكاليف والشحن
- عروض الشحن المجاني
- ملخص الطلب التفصيلي

### ❤️ المفضلة
- حفظ المنتجات المفضلة
- إضافة جميع المنتجات للسلة
- عرض شبكي وقائمة

### 👤 الملف الشخصي
- إدارة معلومات الحساب
- تتبع الطلبات
- إدارة العناوين
- إعدادات التطبيق
- الدعم والمساعدة

### 🔐 المصادقة
- تسجيل الدخول والتسجيل
- استعادة كلمة المرور
- دعم تسجيل الدخول الاجتماعي (قريباً)

## 🏗️ البنية التقنية

### التقنيات المستخدمة
- **Flutter 3.24.5** - إطار العمل الأساسي
- **Dart 3.5.4** - لغة البرمجة
- **Riverpod** - إدارة الحالة
- **Hive** - قاعدة البيانات المحلية
- **Dio** - طلبات HTTP
- **Cached Network Image** - تحميل وتخزين الصور
- **JSON Annotation** - تحويل البيانات

### البنية المعمارية
```
lib/
├── core/                    # الملفات الأساسية
│   ├── constants/          # الثوابت
│   ├── theme/             # الثيم والألوان
│   └── utils/             # الأدوات المساعدة
├── features/              # الميزات الرئيسية
│   ├── splash/           # شاشة البداية
│   ├── main/             # الشاشة الرئيسية والتنقل
│   ├── home/             # الصفحة الرئيسية
│   ├── categories/       # الفئات
│   ├── products/         # المنتجات والبحث
│   ├── cart/             # سلة التسوق
│   ├── wishlist/         # المفضلة
│   ├── profile/          # الملف الشخصي
│   └── auth/             # المصادقة
├── shared/               # المكونات المشتركة
│   ├── models/          # نماذج البيانات
│   └── widgets/         # الويدجت المشتركة
└── main.dart            # نقطة البداية
```

## 🚀 التشغيل

### المتطلبات
- Flutter SDK 3.24.5 أو أحدث
- Dart SDK 3.5.4 أو أحدث
- Android Studio أو VS Code
- جهاز Android/iOS أو محاكي

### خطوات التشغيل

1. **استنساخ المشروع**
```bash
git clone https://github.com/your-username/visionlens-app.git
cd visionlens-app
```

2. **تثبيت المكتبات**
```bash
flutter pub get
```

3. **توليد الملفات المطلوبة**
```bash
flutter packages pub run build_runner build
```

4. **تشغيل التطبيق**
```bash
flutter run
```

## 📦 المكتبات المستخدمة

### الأساسية
- `flutter_riverpod: ^2.5.1` - إدارة الحالة
- `go_router: ^14.2.7` - التنقل
- `hive_flutter: ^1.1.0` - قاعدة البيانات المحلية

### الشبكة والبيانات
- `dio: ^5.7.0` - طلبات HTTP
- `json_annotation: ^4.9.0` - تحويل JSON
- `json_serializable: ^6.8.0` - توليد كود JSON

### واجهة المستخدم
- `cached_network_image: ^3.4.1` - تحميل الصور
- `flutter_svg: ^2.0.10+1` - دعم SVG
- `shimmer: ^3.0.0` - تأثيرات التحميل

### الأدوات
- `intl: ^0.20.2` - التدويل والتواريخ
- `url_launcher: ^6.3.1` - فتح الروابط
- `share_plus: ^10.0.2` - مشاركة المحتوى

## 🎨 التصميم

### نظام الألوان
- **الأساسي**: `#2E86AB` (أزرق العدسات)
- **الثانوي**: `#A23B72` (وردي أنيق)
- **التمييز**: `#F18F01` (برتقالي دافئ)
- **النجاح**: `#27AE60`
- **الخطأ**: `#E74C3C`

### الخطوط
- **الأساسي**: Cairo (عربي)
- **الثانوي**: Tajawal (عربي)

### المبادئ التصميمية
- Material Design 3
- دعم كامل للغة العربية (RTL)
- تصميم متجاوب
- إمكانية الوصول

## 🌐 التدويل

التطبيق يدعم:
- العربية (الافتراضية)
- الإنجليزية (قريباً)

## 📱 المنصات المدعومة

- ✅ Android
- ✅ iOS
- ✅ Web
- ⏳ Windows (قريباً)
- ⏳ macOS (قريباً)
- ⏳ Linux (قريباً)

---

**عدستي - رؤية واضحة، جودة عالية** 👁️✨
