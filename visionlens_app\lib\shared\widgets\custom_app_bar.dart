import 'package:flutter/material.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final bool showBackButton;
  final bool showCartIcon;
  final bool showSearchIcon;
  final bool showNotificationIcon;
  final VoidCallback? onBackPressed;
  final VoidCallback? onCartPressed;
  final VoidCallback? onSearchPressed;
  final VoidCallback? onNotificationPressed;
  final List<Widget>? actions;
  final int cartItemCount;
  final bool hasNotifications;

  const CustomAppBar({
    super.key,
    required this.title,
    this.showBackButton = true,
    this.showCartIcon = false,
    this.showSearchIcon = false,
    this.showNotificationIcon = false,
    this.onBackPressed,
    this.onCartPressed,
    this.onSearchPressed,
    this.onNotificationPressed,
    this.actions,
    this.cartItemCount = 0,
    this.hasNotifications = false,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: AppColors.surface,
      foregroundColor: AppColors.textPrimary,
      elevation: 0,
      scrolledUnderElevation: 1,
      centerTitle: true,
      
      // Leading
      leading: showBackButton
          ? IconButton(
              icon: const Icon(Icons.arrow_back_ios),
              onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
            )
          : null,
      
      // Title
      title: Text(
        title,
        style: AppTextStyles.appBarTitle,
      ),
      
      // Actions
      actions: _buildActions(),
    );
  }

  List<Widget> _buildActions() {
    final actionsList = <Widget>[];

    // Search Icon
    if (showSearchIcon) {
      actionsList.add(
        IconButton(
          icon: const Icon(Icons.search),
          onPressed: onSearchPressed,
        ),
      );
    }

    // Notification Icon
    if (showNotificationIcon) {
      actionsList.add(
        Stack(
          children: [
            IconButton(
              icon: const Icon(Icons.notifications_outlined),
              onPressed: onNotificationPressed,
            ),
            if (hasNotifications)
              Positioned(
                right: 8,
                top: 8,
                child: Container(
                  width: 8,
                  height: 8,
                  decoration: const BoxDecoration(
                    color: AppColors.error,
                    shape: BoxShape.circle,
                  ),
                ),
              ),
          ],
        ),
      );
    }

    // Cart Icon
    if (showCartIcon) {
      actionsList.add(
        Stack(
          children: [
            IconButton(
              icon: const Icon(Icons.shopping_cart_outlined),
              onPressed: onCartPressed,
            ),
            if (cartItemCount > 0)
              Positioned(
                right: 6,
                top: 6,
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: const BoxDecoration(
                    color: AppColors.cartBadge,
                    shape: BoxShape.circle,
                  ),
                  constraints: const BoxConstraints(
                    minWidth: 18,
                    minHeight: 18,
                  ),
                  child: Text(
                    cartItemCount > 99 ? '99+' : cartItemCount.toString(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        ),
      );
    }

    // Custom Actions
    if (actions != null) {
      actionsList.addAll(actions!);
    }

    return actionsList;
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

// Sliver App Bar version for scrollable pages
class CustomSliverAppBar extends StatelessWidget {
  final String title;
  final bool pinned;
  final bool floating;
  final bool snap;
  final double expandedHeight;
  final Widget? flexibleSpace;
  final bool showCartIcon;
  final bool showSearchIcon;
  final bool showNotificationIcon;
  final VoidCallback? onCartPressed;
  final VoidCallback? onSearchPressed;
  final VoidCallback? onNotificationPressed;
  final int cartItemCount;
  final bool hasNotifications;

  const CustomSliverAppBar({
    super.key,
    required this.title,
    this.pinned = true,
    this.floating = false,
    this.snap = false,
    this.expandedHeight = 200.0,
    this.flexibleSpace,
    this.showCartIcon = false,
    this.showSearchIcon = false,
    this.showNotificationIcon = false,
    this.onCartPressed,
    this.onSearchPressed,
    this.onNotificationPressed,
    this.cartItemCount = 0,
    this.hasNotifications = false,
  });

  @override
  Widget build(BuildContext context) {
    return SliverAppBar(
      backgroundColor: AppColors.surface,
      foregroundColor: AppColors.textPrimary,
      elevation: 0,
      scrolledUnderElevation: 1,
      pinned: pinned,
      floating: floating,
      snap: snap,
      expandedHeight: expandedHeight,
      centerTitle: true,
      
      // Title
      title: Text(
        title,
        style: AppTextStyles.appBarTitle,
      ),
      
      // Flexible Space
      flexibleSpace: flexibleSpace,
      
      // Actions
      actions: _buildActions(),
    );
  }

  List<Widget> _buildActions() {
    final actionsList = <Widget>[];

    // Search Icon
    if (showSearchIcon) {
      actionsList.add(
        IconButton(
          icon: const Icon(Icons.search),
          onPressed: onSearchPressed,
        ),
      );
    }

    // Notification Icon
    if (showNotificationIcon) {
      actionsList.add(
        Stack(
          children: [
            IconButton(
              icon: const Icon(Icons.notifications_outlined),
              onPressed: onNotificationPressed,
            ),
            if (hasNotifications)
              Positioned(
                right: 8,
                top: 8,
                child: Container(
                  width: 8,
                  height: 8,
                  decoration: const BoxDecoration(
                    color: AppColors.error,
                    shape: BoxShape.circle,
                  ),
                ),
              ),
          ],
        ),
      );
    }

    // Cart Icon
    if (showCartIcon) {
      actionsList.add(
        Stack(
          children: [
            IconButton(
              icon: const Icon(Icons.shopping_cart_outlined),
              onPressed: onCartPressed,
            ),
            if (cartItemCount > 0)
              Positioned(
                right: 6,
                top: 6,
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: const BoxDecoration(
                    color: AppColors.cartBadge,
                    shape: BoxShape.circle,
                  ),
                  constraints: const BoxConstraints(
                    minWidth: 18,
                    minHeight: 18,
                  ),
                  child: Text(
                    cartItemCount > 99 ? '99+' : cartItemCount.toString(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        ),
      );
    }

    return actionsList;
  }
}
