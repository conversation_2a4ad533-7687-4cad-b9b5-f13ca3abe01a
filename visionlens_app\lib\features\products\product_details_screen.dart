import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../core/constants/app_constants.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../shared/widgets/custom_app_bar.dart';

class ProductDetailsScreen extends ConsumerStatefulWidget {
  final Map<String, dynamic> product;

  const ProductDetailsScreen({
    super.key,
    required this.product,
  });

  @override
  ConsumerState<ProductDetailsScreen> createState() => _ProductDetailsScreenState();
}

class _ProductDetailsScreenState extends ConsumerState<ProductDetailsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  int _selectedImageIndex = 0;
  int _quantity = 1;
  bool _isFavorite = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final product = widget.product;
    final hasDiscount = product['originalPrice'] != null && 
                       product['originalPrice'] > product['price'];

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: CustomAppBar(
        title: product['name'],
        showCartIcon: true,
        actions: [
          IconButton(
            onPressed: _shareProduct,
            icon: const Icon(Icons.share),
          ),
        ],
      ),
      body: Column(
        children: [
          // Product Images and Details
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Image Gallery
                  _buildImageGallery(),
                  
                  // Product Info
                  _buildProductInfo(hasDiscount),
                  
                  // Quantity Selector
                  _buildQuantitySelector(),
                  
                  // Tabs (Description, Specifications, Reviews)
                  _buildTabSection(),
                ],
              ),
            ),
          ),
          
          // Bottom Action Bar
          _buildBottomActionBar(),
        ],
      ),
    );
  }

  Widget _buildImageGallery() {
    final images = widget.product['images'] ?? [widget.product['image']];
    
    return Container(
      height: 400,
      color: AppColors.surface,
      child: Column(
        children: [
          // Main Image
          Expanded(
            child: PageView.builder(
              itemCount: images.length,
              onPageChanged: (index) {
                setState(() {
                  _selectedImageIndex = index;
                });
              },
              itemBuilder: (context, index) {
                return Container(
                  margin: const EdgeInsets.all(16),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: CachedNetworkImage(
                      imageUrl: images[index] ?? 'https://via.placeholder.com/400x400',
                      fit: BoxFit.cover,
                      placeholder: (context, url) => const Center(
                        child: CircularProgressIndicator(),
                      ),
                      errorWidget: (context, url, error) => const Icon(
                        Icons.image_not_supported,
                        size: 100,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          
          // Image Indicators
          if (images.length > 1)
            Container(
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: images.asMap().entries.map((entry) {
                  final index = entry.key;
                  final isActive = index == _selectedImageIndex;
                  
                  return Container(
                    width: isActive ? 24 : 8,
                    height: 8,
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    decoration: BoxDecoration(
                      color: isActive ? AppColors.primary : AppColors.border,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  );
                }).toList(),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildProductInfo(bool hasDiscount) {
    final product = widget.product;
    
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Product Name and Favorite
          Row(
            children: [
              Expanded(
                child: Text(
                  product['name'],
                  style: AppTextStyles.headlineSmall.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              IconButton(
                onPressed: () {
                  setState(() {
                    _isFavorite = !_isFavorite;
                  });
                },
                icon: Icon(
                  _isFavorite ? Icons.favorite : Icons.favorite_border,
                  color: _isFavorite ? AppColors.error : AppColors.textSecondary,
                ),
                style: IconButton.styleFrom(
                  backgroundColor: AppColors.surfaceVariant,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // Brand
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              product['brand'] ?? 'عدستي',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Rating
          if (product['rating'] != null)
            Row(
              children: [
                ...List.generate(5, (index) {
                  return Icon(
                    index < (product['rating'] ?? 0).floor()
                        ? Icons.star
                        : Icons.star_border,
                    color: AppColors.ratingFilled,
                    size: 20,
                  );
                }),
                const SizedBox(width: 8),
                Text(
                  '${product['rating'].toStringAsFixed(1)} (${product['reviewCount'] ?? 0} تقييم)',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          
          const SizedBox(height: 16),
          
          // Price
          Row(
            children: [
              Text(
                '${product['price'].toStringAsFixed(0)} ${AppConstants.currencySymbol}',
                style: AppTextStyles.priceLarge,
              ),
              if (hasDiscount) ...[
                const SizedBox(width: 12),
                Text(
                  '${product['originalPrice'].toStringAsFixed(0)} ${AppConstants.currencySymbol}',
                  style: AppTextStyles.priceDiscounted.copyWith(
                    fontSize: 16,
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.discount,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    '-${(((product['originalPrice'] - product['price']) / product['originalPrice']) * 100).round()}%',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Stock Status
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: product['isInStock'] 
                  ? AppColors.success.withOpacity(0.1)
                  : AppColors.error.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: product['isInStock'] 
                    ? AppColors.success.withOpacity(0.3)
                    : AppColors.error.withOpacity(0.3),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  product['isInStock'] ? Icons.check_circle : Icons.error,
                  color: product['isInStock'] ? AppColors.success : AppColors.error,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  product['isInStock'] ? 'متوفر في المخزون' : 'نفد المخزون',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: product['isInStock'] ? AppColors.success : AppColors.error,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuantitySelector() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.border),
      ),
      child: Row(
        children: [
          Text(
            'الكمية:',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          
          const Spacer(),
          
          // Quantity Controls
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.border),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  onPressed: _quantity > 1 ? () {
                    setState(() {
                      _quantity--;
                    });
                  } : null,
                  icon: const Icon(Icons.remove),
                  style: IconButton.styleFrom(
                    foregroundColor: AppColors.textSecondary,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Text(
                    _quantity.toString(),
                    style: AppTextStyles.titleMedium.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: widget.product['isInStock'] ? () {
                    setState(() {
                      _quantity++;
                    });
                  } : null,
                  icon: const Icon(Icons.add),
                  style: IconButton.styleFrom(
                    foregroundColor: AppColors.primary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabSection() {
    return Container(
      margin: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          // Tab Bar
          TabBar(
            controller: _tabController,
            labelColor: AppColors.primary,
            unselectedLabelColor: AppColors.textSecondary,
            indicatorColor: AppColors.primary,
            tabs: const [
              Tab(text: 'الوصف'),
              Tab(text: 'المواصفات'),
              Tab(text: 'التقييمات'),
            ],
          ),
          
          // Tab Content
          SizedBox(
            height: 300,
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildDescriptionTab(),
                _buildSpecificationsTab(),
                _buildReviewsTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDescriptionTab() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: SingleChildScrollView(
        child: Text(
          widget.product['description'] ?? 
          'عدسات لاصقة عالية الجودة توفر راحة استثنائية ووضوح رؤية ممتاز. مصنوعة من مواد متطورة تضمن الأمان والراحة طوال اليوم.',
          style: AppTextStyles.bodyMedium.copyWith(
            height: 1.6,
          ),
        ),
      ),
    );
  }

  Widget _buildSpecificationsTab() {
    final specs = [
      {'label': 'النوع', 'value': widget.product['type'] ?? 'يومية'},
      {'label': 'المادة', 'value': widget.product['material'] ?? 'سيليكون هيدروجيل'},
      {'label': 'محتوى الماء', 'value': widget.product['waterContent'] ?? '58%'},
      {'label': 'القطر', 'value': widget.product['diameter'] ?? '14.2 مم'},
      {'label': 'انحناء القاعدة', 'value': widget.product['baseCurve'] ?? '8.5 مم'},
      {'label': 'نطاق القوة', 'value': widget.product['powerRange'] ?? '-0.50 إلى -12.00'},
    ];

    return Padding(
      padding: const EdgeInsets.all(20),
      child: ListView.separated(
        itemCount: specs.length,
        separatorBuilder: (context, index) => const Divider(),
        itemBuilder: (context, index) {
          final spec = specs[index];
          return Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                spec['label']!,
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                spec['value']!,
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildReviewsTab() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // Reviews Summary
          Row(
            children: [
              Text(
                '${widget.product['rating']?.toStringAsFixed(1) ?? '4.5'}',
                style: AppTextStyles.headlineMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(width: 12),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: List.generate(5, (index) {
                      return Icon(
                        index < (widget.product['rating'] ?? 4.5).floor()
                            ? Icons.star
                            : Icons.star_border,
                        color: AppColors.ratingFilled,
                        size: 16,
                      );
                    }),
                  ),
                  Text(
                    '${widget.product['reviewCount'] ?? 0} تقييم',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          // Add Review Button
          SizedBox(
            width: double.infinity,
            child: OutlinedButton(
              onPressed: _addReview,
              child: const Text('إضافة تقييم'),
            ),
          ),
          
          const SizedBox(height: 20),
          
          // Reviews List (placeholder)
          Expanded(
            child: Center(
              child: Text(
                'لا توجد تقييمات بعد',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActionBar() {
    final total = widget.product['price'] * _quantity;
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            // Total Price
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'الإجمالي',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                Text(
                  '${total.toStringAsFixed(0)} ${AppConstants.currencySymbol}',
                  style: AppTextStyles.titleLarge.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(width: 20),
            
            // Add to Cart Button
            Expanded(
              child: ElevatedButton(
                onPressed: widget.product['isInStock'] ? _addToCart : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.shopping_cart),
                    const SizedBox(width: 8),
                    Text(
                      widget.product['isInStock'] 
                          ? 'إضافة للسلة'
                          : 'نفد المخزون',
                      style: AppTextStyles.buttonPrimary,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _shareProduct() {
    // TODO: Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('مشاركة المنتج...'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _addToCart() {
    // TODO: Add to cart logic
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم إضافة $_quantity من ${widget.product['name']} إلى السلة'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _addReview() {
    // TODO: Navigate to add review screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('إضافة تقييم...'),
        backgroundColor: AppColors.primary,
      ),
    );
  }
}
