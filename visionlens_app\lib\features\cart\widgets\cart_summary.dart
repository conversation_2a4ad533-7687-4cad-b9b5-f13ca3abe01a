import 'package:flutter/material.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';

class CartSummary extends StatelessWidget {
  final double subtotal;
  final double shipping;
  final double tax;
  final double total;
  final int itemCount;

  const CartSummary({
    super.key,
    required this.subtotal,
    required this.shipping,
    required this.tax,
    required this.total,
    required this.itemCount,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surfaceVariant,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'ملخص الطلب',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Items Count
          _buildSummaryRow(
            'عدد المنتجات',
            '$itemCount منتج',
            isHighlight: false,
          ),
          
          const SizedBox(height: 8),
          
          // Subtotal
          _buildSummaryRow(
            'المجموع الفرعي',
            '${subtotal.toStringAsFixed(0)} ${AppConstants.currencySymbol}',
            isHighlight: false,
          ),
          
          const SizedBox(height: 8),
          
          // Shipping
          _buildSummaryRow(
            'تكلفة الشحن',
            shipping == 0 
                ? 'مجاني' 
                : '${shipping.toStringAsFixed(0)} ${AppConstants.currencySymbol}',
            isHighlight: false,
            showFreeShippingInfo: shipping == 0 && subtotal >= AppConstants.freeShippingThreshold,
          ),
          
          // Free Shipping Progress
          if (shipping > 0 && subtotal < AppConstants.freeShippingThreshold)
            _buildFreeShippingProgress(),
          
          const SizedBox(height: 8),
          
          // Tax (if applicable)
          if (tax > 0) ...[
            _buildSummaryRow(
              'الضريبة',
              '${tax.toStringAsFixed(0)} ${AppConstants.currencySymbol}',
              isHighlight: false,
            ),
            const SizedBox(height: 8),
          ],
          
          // Divider
          const Divider(color: AppColors.border),
          
          const SizedBox(height: 8),
          
          // Total
          _buildSummaryRow(
            'الإجمالي',
            '${total.toStringAsFixed(0)} ${AppConstants.currencySymbol}',
            isHighlight: true,
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(
    String label,
    String value, {
    bool isHighlight = false,
    bool showFreeShippingInfo = false,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: isHighlight
                  ? AppTextStyles.titleMedium.copyWith(
                      fontWeight: FontWeight.bold,
                    )
                  : AppTextStyles.bodyMedium,
            ),
            if (showFreeShippingInfo)
              Text(
                'شحن مجاني للطلبات أكثر من ${AppConstants.freeShippingThreshold.toStringAsFixed(0)} ${AppConstants.currencySymbol}',
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.success,
                ),
              ),
          ],
        ),
        Text(
          value,
          style: isHighlight
              ? AppTextStyles.titleMedium.copyWith(
                  color: AppColors.primary,
                  fontWeight: FontWeight.bold,
                )
              : AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
        ),
      ],
    );
  }

  Widget _buildFreeShippingProgress() {
    final remaining = AppConstants.freeShippingThreshold - subtotal;
    final progress = subtotal / AppConstants.freeShippingThreshold;
    
    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.success.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.success.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.local_shipping,
                color: AppColors.success,
                size: 16,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'أضف ${remaining.toStringAsFixed(0)} ${AppConstants.currencySymbol} للحصول على شحن مجاني',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.success,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          // Progress Bar
          LinearProgressIndicator(
            value: progress.clamp(0.0, 1.0),
            backgroundColor: AppColors.success.withOpacity(0.2),
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.success),
            borderRadius: BorderRadius.circular(4),
          ),
        ],
      ),
    );
  }
}
