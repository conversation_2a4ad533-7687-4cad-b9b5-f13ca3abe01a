import 'package:json_annotation/json_annotation.dart';

part 'product_model.g.dart';

@JsonSerializable()
class Product {
  final int id;
  final String name;
  final String description;
  final double price;
  @JsonKey(name: 'discount_price')
  final double? discountPrice;
  final Category category;
  final Brand brand;
  @JsonKey(name: 'lens_type')
  final String lensType;
  @JsonKey(name: 'lens_usage')
  final String lensUsage;
  @JsonKey(name: 'power_range')
  final String? powerRange;
  final double? diameter;
  @JsonKey(name: 'base_curve')
  final double? baseCurve;
  @JsonKey(name: 'water_content')
  final double? waterContent;
  final String? material;
  final String? color;
  @JsonKey(name: 'stock_quantity')
  final int stockQuantity;
  @<PERSON>son<PERSON>ey(name: 'is_active')
  final bool isActive;
  @<PERSON>sonKey(name: 'created_at')
  final DateTime createdAt;
  @Json<PERSON>ey(name: 'updated_at')
  final DateTime updatedAt;
  final String? image;
  final List<String>? images;
  final double? rating;
  @<PERSON><PERSON><PERSON>ey(name: 'review_count')
  final int reviewCount;
  @<PERSON>son<PERSON>ey(name: 'is_featured')
  final bool isFeatured;
  @<PERSON>son<PERSON>ey(name: 'is_bestseller')
  final bool isBestseller;

  const Product({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    this.discountPrice,
    required this.category,
    required this.brand,
    required this.lensType,
    required this.lensUsage,
    this.powerRange,
    this.diameter,
    this.baseCurve,
    this.waterContent,
    this.material,
    this.color,
    required this.stockQuantity,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    this.image,
    this.images,
    this.rating,
    this.reviewCount = 0,
    this.isFeatured = false,
    this.isBestseller = false,
  });

  factory Product.fromJson(Map<String, dynamic> json) => _$ProductFromJson(json);
  Map<String, dynamic> toJson() => _$ProductToJson(this);

  double get finalPrice => discountPrice ?? price;
  bool get hasDiscount => discountPrice != null && discountPrice! < price;
  double get discountPercentage => hasDiscount ? ((price - discountPrice!) / price * 100) : 0;
  bool get isInStock => stockQuantity > 0;
  bool get isLowStock => stockQuantity > 0 && stockQuantity <= 5;
  String get stockStatus {
    if (stockQuantity <= 0) return 'نفد المخزون';
    if (stockQuantity <= 5) return 'مخزون قليل';
    return 'متوفر';
  }

  Product copyWith({
    int? id,
    String? name,
    String? description,
    double? price,
    double? discountPrice,
    Category? category,
    Brand? brand,
    String? lensType,
    String? lensUsage,
    String? powerRange,
    double? diameter,
    double? baseCurve,
    double? waterContent,
    String? material,
    String? color,
    int? stockQuantity,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? image,
    List<String>? images,
    double? rating,
    int? reviewCount,
    bool? isFeatured,
    bool? isBestseller,
  }) {
    return Product(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      discountPrice: discountPrice ?? this.discountPrice,
      category: category ?? this.category,
      brand: brand ?? this.brand,
      lensType: lensType ?? this.lensType,
      lensUsage: lensUsage ?? this.lensUsage,
      powerRange: powerRange ?? this.powerRange,
      diameter: diameter ?? this.diameter,
      baseCurve: baseCurve ?? this.baseCurve,
      waterContent: waterContent ?? this.waterContent,
      material: material ?? this.material,
      color: color ?? this.color,
      stockQuantity: stockQuantity ?? this.stockQuantity,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      image: image ?? this.image,
      images: images ?? this.images,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      isFeatured: isFeatured ?? this.isFeatured,
      isBestseller: isBestseller ?? this.isBestseller,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Product && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Product(id: $id, name: $name, price: $price, category: ${category.name})';
  }
}

@JsonSerializable()
class Category {
  final int id;
  final String name;
  final String description;
  final String? image;
  @JsonKey(name: 'is_active')
  final bool isActive;
  @JsonKey(name: 'sort_order')
  final int sortOrder;
  @JsonKey(name: 'product_count')
  final int productCount;

  const Category({
    required this.id,
    required this.name,
    required this.description,
    this.image,
    required this.isActive,
    required this.sortOrder,
    this.productCount = 0,
  });

  factory Category.fromJson(Map<String, dynamic> json) => _$CategoryFromJson(json);
  Map<String, dynamic> toJson() => _$CategoryToJson(this);

  Category copyWith({
    int? id,
    String? name,
    String? description,
    String? image,
    bool? isActive,
    int? sortOrder,
    int? productCount,
  }) {
    return Category(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      image: image ?? this.image,
      isActive: isActive ?? this.isActive,
      sortOrder: sortOrder ?? this.sortOrder,
      productCount: productCount ?? this.productCount,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Category && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Category(id: $id, name: $name, productCount: $productCount)';
  }
}

@JsonSerializable()
class Brand {
  final int id;
  final String name;
  final String description;
  final String? logo;
  final String? website;
  @JsonKey(name: 'is_active')
  final bool isActive;
  @JsonKey(name: 'product_count')
  final int productCount;

  const Brand({
    required this.id,
    required this.name,
    required this.description,
    this.logo,
    this.website,
    required this.isActive,
    this.productCount = 0,
  });

  factory Brand.fromJson(Map<String, dynamic> json) => _$BrandFromJson(json);
  Map<String, dynamic> toJson() => _$BrandToJson(this);

  Brand copyWith({
    int? id,
    String? name,
    String? description,
    String? logo,
    String? website,
    bool? isActive,
    int? productCount,
  }) {
    return Brand(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      logo: logo ?? this.logo,
      website: website ?? this.website,
      isActive: isActive ?? this.isActive,
      productCount: productCount ?? this.productCount,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Brand && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Brand(id: $id, name: $name, productCount: $productCount)';
  }
}
