{"buildFiles": ["C:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "G:\\visionlensapp\\build\\.cxx\\Debug\\133cx1u6\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "G:\\visionlensapp\\build\\.cxx\\Debug\\133cx1u6\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}