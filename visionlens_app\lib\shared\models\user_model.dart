import 'package:json_annotation/json_annotation.dart';

part 'user_model.g.dart';

@JsonSerializable()
class User {
  final int id;
  final String username;
  final String email;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'first_name')
  final String firstName;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'last_name')
  final String lastName;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_active')
  final bool isActive;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'date_joined')
  final DateTime dateJoined;
  final UserProfile? profile;

  const User({
    required this.id,
    required this.username,
    required this.email,
    required this.firstName,
    required this.lastName,
    required this.isActive,
    required this.dateJoined,
    this.profile,
  });

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
  Map<String, dynamic> toJson() => _$UserToJson(this);

  String get fullName => '$firstName $lastName';

  User copyWith({
    int? id,
    String? username,
    String? email,
    String? firstName,
    String? lastName,
    bool? isActive,
    DateTime? dateJoined,
    UserProfile? profile,
  }) {
    return User(
      id: id ?? this.id,
      username: username ?? this.username,
      email: email ?? this.email,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      isActive: isActive ?? this.isActive,
      dateJoined: dateJoined ?? this.dateJoined,
      profile: profile ?? this.profile,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is User && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'User(id: $id, username: $username, email: $email, fullName: $fullName)';
  }
}

@JsonSerializable()
class UserProfile {
  final String? phone;
  @JsonKey(name: 'birth_date')
  final DateTime? birthDate;
  final String? gender;
  @JsonKey(name: 'profile_image')
  final String? profileImage;
  @JsonKey(name: 'photo_url')
  final String? photoUrl;
  @JsonKey(name: 'preferred_language')
  final String preferredLanguage;
  @JsonKey(name: 'newsletter_subscription')
  final bool newsletterSubscription;
  @JsonKey(name: 'is_email_verified')
  final bool isEmailVerified;

  const UserProfile({
    this.phone,
    this.birthDate,
    this.gender,
    this.profileImage,
    this.photoUrl,
    this.preferredLanguage = 'ar',
    this.newsletterSubscription = false,
    this.isEmailVerified = false,
  });

  factory UserProfile.fromJson(Map<String, dynamic> json) =>
      _$UserProfileFromJson(json);
  Map<String, dynamic> toJson() => _$UserProfileToJson(this);

  UserProfile copyWith({
    String? phone,
    DateTime? birthDate,
    String? gender,
    String? profileImage,
    String? photoUrl,
    String? preferredLanguage,
    bool? newsletterSubscription,
    bool? isEmailVerified,
  }) {
    return UserProfile(
      phone: phone ?? this.phone,
      birthDate: birthDate ?? this.birthDate,
      gender: gender ?? this.gender,
      profileImage: profileImage ?? this.profileImage,
      photoUrl: photoUrl ?? this.photoUrl,
      preferredLanguage: preferredLanguage ?? this.preferredLanguage,
      newsletterSubscription:
          newsletterSubscription ?? this.newsletterSubscription,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserProfile &&
        other.phone == phone &&
        other.birthDate == birthDate &&
        other.gender == gender &&
        other.profileImage == profileImage &&
        other.photoUrl == photoUrl &&
        other.preferredLanguage == preferredLanguage &&
        other.newsletterSubscription == newsletterSubscription &&
        other.isEmailVerified == isEmailVerified;
  }

  @override
  int get hashCode {
    return Object.hash(
      phone,
      birthDate,
      gender,
      profileImage,
      photoUrl,
      preferredLanguage,
      newsletterSubscription,
      isEmailVerified,
    );
  }

  @override
  String toString() {
    return 'UserProfile(phone: $phone, gender: $gender, language: $preferredLanguage)';
  }
}

@JsonSerializable()
class AuthResponse {
  final String token;
  final User user;
  @JsonKey(name: 'refresh_token')
  final String? refreshToken;
  @JsonKey(name: 'expires_at')
  final DateTime? expiresAt;

  const AuthResponse({
    required this.token,
    required this.user,
    this.refreshToken,
    this.expiresAt,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) =>
      _$AuthResponseFromJson(json);
  Map<String, dynamic> toJson() => _$AuthResponseToJson(this);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AuthResponse &&
        other.token == token &&
        other.user == user &&
        other.refreshToken == refreshToken &&
        other.expiresAt == expiresAt;
  }

  @override
  int get hashCode {
    return Object.hash(token, user, refreshToken, expiresAt);
  }

  @override
  String toString() {
    return 'AuthResponse(token: ${token.substring(0, 10)}..., user: ${user.username})';
  }
}

@JsonSerializable()
class LoginRequest {
  final String email;
  final String password;

  const LoginRequest({required this.email, required this.password});

  factory LoginRequest.fromJson(Map<String, dynamic> json) =>
      _$LoginRequestFromJson(json);
  Map<String, dynamic> toJson() => _$LoginRequestToJson(this);
}

@JsonSerializable()
class RegisterRequest {
  final String username;
  final String email;
  final String password;
  @JsonKey(name: 'first_name')
  final String firstName;
  @JsonKey(name: 'last_name')
  final String lastName;
  final String? phone;

  const RegisterRequest({
    required this.username,
    required this.email,
    required this.password,
    required this.firstName,
    required this.lastName,
    this.phone,
  });

  factory RegisterRequest.fromJson(Map<String, dynamic> json) =>
      _$RegisterRequestFromJson(json);
  Map<String, dynamic> toJson() => _$RegisterRequestToJson(this);
}

@JsonSerializable()
class PasswordResetRequest {
  final String email;

  const PasswordResetRequest({required this.email});

  factory PasswordResetRequest.fromJson(Map<String, dynamic> json) =>
      _$PasswordResetRequestFromJson(json);
  Map<String, dynamic> toJson() => _$PasswordResetRequestToJson(this);
}

@JsonSerializable()
class ChangePasswordRequest {
  @JsonKey(name: 'old_password')
  final String oldPassword;
  @JsonKey(name: 'new_password')
  final String newPassword;

  const ChangePasswordRequest({
    required this.oldPassword,
    required this.newPassword,
  });

  factory ChangePasswordRequest.fromJson(Map<String, dynamic> json) =>
      _$ChangePasswordRequestFromJson(json);
  Map<String, dynamic> toJson() => _$ChangePasswordRequestToJson(this);
}
