// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Order _$OrderFromJson(Map<String, dynamic> json) => Order(
      id: (json['id'] as num).toInt(),
      orderNumber: json['order_number'] as String,
      user: User.fromJson(json['user'] as Map<String, dynamic>),
      status: json['status'] as String,
      paymentStatus: json['payment_status'] as String,
      shippingName: json['shipping_name'] as String,
      shippingPhone: json['shipping_phone'] as String,
      shippingAddress: json['shipping_address'] as String,
      shippingCity: json['shipping_city'] as String,
      shippingPostalCode: json['shipping_postal_code'] as String?,
      subtotal: (json['subtotal'] as num).toDouble(),
      shippingCost: (json['shipping_cost'] as num).toDouble(),
      taxAmount: (json['tax_amount'] as num).toDouble(),
      totalAmount: (json['total_amount'] as num).toDouble(),
      notes: json['notes'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      items: (json['items'] as List<dynamic>)
          .map((e) => OrderItem.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$OrderToJson(Order instance) => <String, dynamic>{
      'id': instance.id,
      'order_number': instance.orderNumber,
      'user': instance.user,
      'status': instance.status,
      'payment_status': instance.paymentStatus,
      'shipping_name': instance.shippingName,
      'shipping_phone': instance.shippingPhone,
      'shipping_address': instance.shippingAddress,
      'shipping_city': instance.shippingCity,
      'shipping_postal_code': instance.shippingPostalCode,
      'subtotal': instance.subtotal,
      'shipping_cost': instance.shippingCost,
      'tax_amount': instance.taxAmount,
      'total_amount': instance.totalAmount,
      'notes': instance.notes,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
      'items': instance.items,
    };

OrderItem _$OrderItemFromJson(Map<String, dynamic> json) => OrderItem(
      id: (json['id'] as num).toInt(),
      orderId: (json['orderId'] as num).toInt(),
      product: Product.fromJson(json['product'] as Map<String, dynamic>),
      quantity: (json['quantity'] as num).toInt(),
      price: (json['price'] as num).toDouble(),
      total: (json['total'] as num).toDouble(),
    );

Map<String, dynamic> _$OrderItemToJson(OrderItem instance) => <String, dynamic>{
      'id': instance.id,
      'orderId': instance.orderId,
      'product': instance.product,
      'quantity': instance.quantity,
      'price': instance.price,
      'total': instance.total,
    };

CreateOrderRequest _$CreateOrderRequestFromJson(Map<String, dynamic> json) =>
    CreateOrderRequest(
      shippingName: json['shipping_name'] as String,
      shippingPhone: json['shipping_phone'] as String,
      shippingAddress: json['shipping_address'] as String,
      shippingCity: json['shipping_city'] as String,
      shippingPostalCode: json['shipping_postal_code'] as String?,
      paymentMethod: json['payment_method'] as String,
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$CreateOrderRequestToJson(CreateOrderRequest instance) =>
    <String, dynamic>{
      'shipping_name': instance.shippingName,
      'shipping_phone': instance.shippingPhone,
      'shipping_address': instance.shippingAddress,
      'shipping_city': instance.shippingCity,
      'shipping_postal_code': instance.shippingPostalCode,
      'payment_method': instance.paymentMethod,
      'notes': instance.notes,
    };

OrdersResponse _$OrdersResponseFromJson(Map<String, dynamic> json) =>
    OrdersResponse(
      orders: (json['orders'] as List<dynamic>)
          .map((e) => Order.fromJson(e as Map<String, dynamic>))
          .toList(),
      total: (json['total'] as num).toInt(),
      page: (json['page'] as num).toInt(),
      perPage: (json['per_page'] as num).toInt(),
      totalPages: (json['total_pages'] as num).toInt(),
    );

Map<String, dynamic> _$OrdersResponseToJson(OrdersResponse instance) =>
    <String, dynamic>{
      'orders': instance.orders,
      'total': instance.total,
      'page': instance.page,
      'per_page': instance.perPage,
      'total_pages': instance.totalPages,
    };

OrderTrackingInfo _$OrderTrackingInfoFromJson(Map<String, dynamic> json) =>
    OrderTrackingInfo(
      orderId: (json['order_id'] as num).toInt(),
      status: json['status'] as String,
      trackingNumber: json['tracking_number'] as String?,
      estimatedDelivery: json['estimated_delivery'] == null
          ? null
          : DateTime.parse(json['estimated_delivery'] as String),
      statusHistory: (json['status_history'] as List<dynamic>)
          .map((e) => OrderStatusHistory.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$OrderTrackingInfoToJson(OrderTrackingInfo instance) =>
    <String, dynamic>{
      'order_id': instance.orderId,
      'status': instance.status,
      'tracking_number': instance.trackingNumber,
      'estimated_delivery': instance.estimatedDelivery?.toIso8601String(),
      'status_history': instance.statusHistory,
    };

OrderStatusHistory _$OrderStatusHistoryFromJson(Map<String, dynamic> json) =>
    OrderStatusHistory(
      status: json['status'] as String,
      description: json['description'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
    );

Map<String, dynamic> _$OrderStatusHistoryToJson(OrderStatusHistory instance) =>
    <String, dynamic>{
      'status': instance.status,
      'description': instance.description,
      'created_at': instance.createdAt.toIso8601String(),
    };
