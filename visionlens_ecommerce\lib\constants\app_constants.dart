import 'package:flutter/material.dart';

class AppConstants {
  // App Info
  static const String appName = 'VisionLens';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'تطبيق العدسات اللاصقة الرائد';

  // Colors
  static const Color primaryColor = Color(0xFF2196F3);
  static const Color secondaryColor = Color(0xFF03DAC6);
  static const Color accentColor = Color(0xFFFF9800);
  static const Color errorColor = Color(0xFFB00020);
  static const Color successColor = Color(0xFF4CAF50);
  static const Color warningColor = Color(0xFFFF9800);
  
  // Background Colors
  static const Color backgroundColor = Color(0xFFF5F5F5);
  static const Color surfaceColor = Color(0xFFFFFFFF);
  static const Color cardColor = Color(0xFFFFFFFF);
  
  // Text Colors
  static const Color textPrimaryColor = Color(0xFF212121);
  static const Color textSecondaryColor = Color(0xFF757575);
  static const Color textHintColor = Color(0xFF9E9E9E);
  
  // Spacing
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;
  
  // Border Radius
  static const double radiusSmall = 4.0;
  static const double radiusMedium = 8.0;
  static const double radiusLarge = 12.0;
  static const double radiusXLarge = 16.0;
  
  // Font Sizes
  static const double fontSizeSmall = 12.0;
  static const double fontSizeMedium = 14.0;
  static const double fontSizeLarge = 16.0;
  static const double fontSizeXLarge = 18.0;
  static const double fontSizeXXLarge = 20.0;
  static const double fontSizeTitle = 24.0;
  static const double fontSizeHeading = 28.0;
  
  // Animation Durations
  static const Duration animationDurationShort = Duration(milliseconds: 200);
  static const Duration animationDurationMedium = Duration(milliseconds: 300);
  static const Duration animationDurationLong = Duration(milliseconds: 500);
  
  // API Endpoints (for future use)
  static const String baseUrl = 'https://api.visionlens.com';
  static const String productsEndpoint = '/products';
  static const String categoriesEndpoint = '/categories';
  static const String ordersEndpoint = '/orders';
  static const String authEndpoint = '/auth';
  
  // Demo Data
  static const String demoCustomerEmail = '<EMAIL>';
  static const String demoCustomerPassword = '123456';
  static const String demoAdminEmail = '<EMAIL>';
  static const String demoAdminPassword = 'admin123';
}

class AppStrings {
  // App General
  static const String appName = 'VisionLens';
  static const String welcome = 'مرحباً بك في VisionLens';
  static const String description = 'أفضل العدسات اللاصقة بأسعار مميزة';
  
  // Navigation
  static const String home = 'الرئيسية';
  static const String products = 'المنتجات';
  static const String categories = 'الفئات';
  static const String cart = 'السلة';
  static const String profile = 'الملف الشخصي';
  static const String settings = 'الإعدادات';
  static const String search = 'البحث';
  
  // Auth
  static const String login = 'تسجيل الدخول';
  static const String register = 'إنشاء حساب';
  static const String logout = 'تسجيل الخروج';
  static const String email = 'البريد الإلكتروني';
  static const String password = 'كلمة المرور';
  static const String confirmPassword = 'تأكيد كلمة المرور';
  static const String forgotPassword = 'نسيت كلمة المرور؟';
  static const String dontHaveAccount = 'ليس لديك حساب؟';
  static const String alreadyHaveAccount = 'لديك حساب؟';
  
  // Products
  static const String addToCart = 'إضافة للسلة';
  static const String buyNow = 'اشتري الآن';
  static const String price = 'السعر';
  static const String brand = 'العلامة التجارية';
  static const String category = 'الفئة';
  static const String description = 'الوصف';
  static const String reviews = 'التقييمات';
  static const String rating = 'التقييم';
  
  // Cart
  static const String emptyCart = 'السلة فارغة';
  static const String total = 'المجموع';
  static const String checkout = 'إتمام الطلب';
  static const String quantity = 'الكمية';
  static const String remove = 'إزالة';
  
  // Messages
  static const String success = 'تم بنجاح';
  static const String error = 'حدث خطأ';
  static const String loading = 'جاري التحميل...';
  static const String noData = 'لا توجد بيانات';
  static const String tryAgain = 'حاول مرة أخرى';
  
  // Validation
  static const String fieldRequired = 'هذا الحقل مطلوب';
  static const String invalidEmail = 'البريد الإلكتروني غير صحيح';
  static const String passwordTooShort = 'كلمة المرور قصيرة جداً';
  static const String passwordsNotMatch = 'كلمات المرور غير متطابقة';
}
