["G:\\visionlensapp\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\vm_snapshot_data", "G:\\visionlensapp\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\isolate_snapshot_data", "G:\\visionlensapp\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\kernel_blob.bin", "G:\\visionlensapp\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/cupertino_icons/assets/CupertinoIcons.ttf", "G:\\visionlensapp\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\fonts/MaterialIcons-Regular.otf", "G:\\visionlensapp\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\shaders/ink_sparkle.frag", "G:\\visionlensapp\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.json", "G:\\visionlensapp\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.bin", "G:\\visionlensapp\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\FontManifest.json", "G:\\visionlensapp\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NOTICES.Z", "G:\\visionlensapp\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NativeAssetsManifest.json"]