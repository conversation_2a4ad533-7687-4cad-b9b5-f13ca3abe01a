import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/theme/app_colors.dart';

import '../../shared/widgets/custom_app_bar.dart';
import '../../shared/widgets/category_card.dart';

class CategoriesScreen extends ConsumerStatefulWidget {
  const CategoriesScreen({super.key});

  @override
  ConsumerState<CategoriesScreen> createState() => _CategoriesScreenState();
}

class _CategoriesScreenState extends ConsumerState<CategoriesScreen> {
  String _searchQuery = '';
  bool _isGridView = true;

  @override
  Widget build(BuildContext context) {
    final filteredCategories = _getFilteredCategories();

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: CustomAppBar(
        title: 'الفئات',
        showBackButton: false,
        showSearchIcon: true,
        showCartIcon: true,
        onSearchPressed: () {
          _showSearchDialog();
        },
        actions: [
          IconButton(
            icon: Icon(
              _isGridView ? Icons.list : Icons.grid_view,
              color: AppColors.textSecondary,
            ),
            onPressed: () {
              setState(() {
                _isGridView = !_isGridView;
              });
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _onRefresh,
        child: Column(
          children: [
            // Search Bar
            if (_searchQuery.isNotEmpty) _buildSearchBar(),

            // Categories List/Grid
            Expanded(
              child: _isGridView
                  ? _buildGridView(filteredCategories)
                  : _buildListView(filteredCategories),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: TextField(
        decoration: InputDecoration(
          hintText: 'ابحث في الفئات...',
          prefixIcon: const Icon(Icons.search, color: AppColors.textSecondary),
          suffixIcon: IconButton(
            icon: const Icon(Icons.clear, color: AppColors.textSecondary),
            onPressed: () {
              setState(() {
                _searchQuery = '';
              });
            },
          ),
          filled: true,
          fillColor: AppColors.surface,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
      ),
    );
  }

  Widget _buildGridView(List<Map<String, dynamic>> categories) {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.8,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: categories.length,
      itemBuilder: (context, index) {
        final category = categories[index];
        return CategoryGridCard(
          title: category['name'],
          icon: category['icon'],
          color: category['color'],
          productCount: category['productCount'],
          onTap: () {
            _navigateToProducts(category);
          },
        );
      },
    );
  }

  Widget _buildListView(List<Map<String, dynamic>> categories) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: categories.length,
      itemBuilder: (context, index) {
        final category = categories[index];
        return CategoryListCard(
          title: category['name'],
          description: category['description'],
          icon: category['icon'],
          color: category['color'],
          productCount: category['productCount'],
          onTap: () {
            _navigateToProducts(category);
          },
        );
      },
    );
  }

  List<Map<String, dynamic>> _getFilteredCategories() {
    final categories = _getDummyCategories();

    if (_searchQuery.isEmpty) {
      return categories;
    }

    return categories.where((category) {
      return category['name'].toLowerCase().contains(
            _searchQuery.toLowerCase(),
          ) ||
          category['description'].toLowerCase().contains(
            _searchQuery.toLowerCase(),
          );
    }).toList();
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('البحث في الفئات'),
          content: TextField(
            decoration: const InputDecoration(
              hintText: 'اكتب اسم الفئة...',
              prefixIcon: Icon(Icons.search),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('بحث'),
            ),
          ],
        );
      },
    );
  }

  void _navigateToProducts(Map<String, dynamic> category) {
    // TODO: Navigate to products screen with category filter
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('الانتقال إلى منتجات ${category['name']}'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  Future<void> _onRefresh() async {
    // TODO: Refresh categories data
    await Future.delayed(const Duration(seconds: 1));
  }

  List<Map<String, dynamic>> _getDummyCategories() {
    return [
      {
        'name': 'عدسات يومية',
        'description': 'عدسات لاصقة للاستخدام اليومي، مريحة وآمنة',
        'icon': Icons.calendar_today,
        'color': AppColors.categoryColors[0],
        'productCount': 45,
      },
      {
        'name': 'عدسات أسبوعية',
        'description': 'عدسات تدوم لأسبوع كامل مع العناية المناسبة',
        'icon': Icons.calendar_view_week,
        'color': AppColors.categoryColors[1],
        'productCount': 28,
      },
      {
        'name': 'عدسات شهرية',
        'description': 'عدسات طويلة المدى للاستخدام الشهري',
        'icon': Icons.calendar_month,
        'color': AppColors.categoryColors[2],
        'productCount': 67,
      },
      {
        'name': 'عدسات ملونة',
        'description': 'عدسات تجميلية بألوان متنوعة وجذابة',
        'icon': Icons.palette,
        'color': AppColors.categoryColors[3],
        'productCount': 89,
      },
      {
        'name': 'عدسات طبية',
        'description': 'عدسات لتصحيح النظر بدرجات مختلفة',
        'icon': Icons.medical_services,
        'color': AppColors.categoryColors[4],
        'productCount': 156,
      },
      {
        'name': 'عدسات تجميلية',
        'description': 'عدسات لتحسين المظهر والجمال',
        'icon': Icons.auto_awesome,
        'color': AppColors.categoryColors[5],
        'productCount': 73,
      },
      {
        'name': 'عدسات رياضية',
        'description': 'عدسات مخصصة للأنشطة الرياضية',
        'icon': Icons.sports,
        'color': AppColors.categoryColors[6],
        'productCount': 34,
      },
      {
        'name': 'محاليل العناية',
        'description': 'محاليل تنظيف وحفظ العدسات اللاصقة',
        'icon': Icons.cleaning_services,
        'color': AppColors.categoryColors[7],
        'productCount': 23,
      },
    ];
  }
}
